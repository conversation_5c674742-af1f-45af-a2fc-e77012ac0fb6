name: Build and Deploy Expo Web to S3

on:
  push:
    branches:
      - develop
      - main
    tags:
      - stg-v*.*.*

jobs:
  build:
    name: Build Expo Web
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      # Set NODE_ENV based on branch or tag
      - name: Set NODE_ENV
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "NODE_ENV=production" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == refs/tags/stg-v*.*.* ]]; then
            echo "NODE_ENV=staging" >> $GITHUB_ENV
          else
            echo "NODE_ENV=development" >> $GITHUB_ENV
          fi

      # Build step always uses production env for build:web
      - name: Build Expo Web
        run: npm run build:web
        env:
          NODE_ENV: ${{ env.NODE_ENV }}

      - name: Upload Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: expo-web-dist
          path: ./dist

      - name: Trigger scribble-testing workflow.
        run: |
          curl -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.TEST_REPO_TOKEN }}" \
            https://api.github.com/repos/Acutedge-Inc/scribble-testing/dispatches \
            -d '{"event_type":"run-build","client_payload":{"source_repo":"${{ github.repository }}","ref":"${{ github.ref }}"}}'          


  deploy-production:
    name: Deploy to S3 (Production)
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: expo-web-dist
          path: ./dist

      - name: Set Production Environment Variables
        run: |
          echo "BUCKET=${{ secrets.PROD_S3_BUCKET }}" >> $GITHUB_ENV
          echo "REGION=${{ secrets.PROD_AWS_REGION }}" >> $GITHUB_ENV
          echo "DISTRIBUTION_ID=${{ secrets.DISTRIBUTION_ID }}" >> $GITHUB_ENV

      - name: Deploy to S3 (Production)
        run: |
          aws s3 sync ./dist s3://$BUCKET --follow-symlinks --delete
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.REGION }}

      - name: Invalidate CloudFront Cache (Production)
        run: |
          aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.REGION }}

  deploy-dev:
    name: Deploy to S3 (Development)
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: expo-web-dist
          path: ./dist

      - name: Set Development Environment Variables
        run: |
          echo "BUCKET=${{ secrets.S3_BUCKET }}" >> $GITHUB_ENV
          echo "REGION=${{ secrets.AWS_REGION }}" >> $GITHUB_ENV
          echo "DISTRIBUTION_ID=${{ secrets.DISTRIBUTION_ID }}" >> $GITHUB_ENV

      - name: Deploy to S3 (Development)
        run: |
          aws s3 sync ./dist s3://$BUCKET --follow-symlinks --delete
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.REGION }}

      - name: Invalidate CloudFront Cache (Development)
        run: |
          aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.REGION }}

  deploy-staging:
    name: Deploy to S3 (Staging)
    if: ${{ startsWith(github.ref, 'refs/tags/stg-v') }}
    runs-on: ubuntu-latest
    environment: Staging
    needs: build
    steps:
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: expo-web-dist
          path: ./dist

      # Set environment variables for staging S3/CloudFront
      - name: Set Staging Environment Variables
        run: |
          echo "BUCKET=${{ vars.S3_BUCKET }}" >> $GITHUB_ENV
          echo "REGION=${{ vars.AWS_REGION }}" >> $GITHUB_ENV
          echo "DISTRIBUTION_ID=${{ vars.DISTRIBUTION_ID }}" >> $GITHUB_ENV

      - name: Deploy to S3 (Staging)
        run: |
          aws s3 sync ./dist s3://$BUCKET --follow-symlinks --delete
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.REGION }}

      - name: Invalidate CloudFront Cache (Staging)
        run: |
          aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.REGION }}
