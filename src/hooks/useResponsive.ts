import '@expo/match-media';
import { useMediaQuery } from 'react-responsive';

/**
 * A custom hook for responsive design checks,
 * using react-responsive + expo match-media.
 */
export function useResponsive() {
  const isTabletOrMobileDevice = useMediaQuery({
    maxDeviceWidth: 1224,
  });

  const isLargeScreen = useMediaQuery({ minDeviceWidth: 1440 });

  return {
    isTabletOrMobileDevice,
    isLargeScreen,
  };
}
