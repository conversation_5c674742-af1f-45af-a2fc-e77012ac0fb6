import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { hideRecorder } from "../redux/slices/audioRecorderSlice";
import { logout } from "../redux/slices/authSlice";
import { logOut } from "../features/profile/profile-api";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import { clearSecureStorage, getSecureItem, setSecureItem } from "../utils/cryptoHelper";
import { useNavigation } from "@react-navigation/native";

export const useLogoutUser = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation()
  const recorderState = useSelector((state: RootState) => state.audioRecorder);


  const WAIT_TIME = 10 * 60 * 1000;
  const logOutUser = async () => {
    try {
      const refreshToken = await getSecureItem("refreshToken");
      console.log("Inside logout")
      const orgName = await getSecureItem("org")
      // const storedRecording = await getSecureItem("recording");
      let resp = await logOut(refreshToken as string);
      //console.log("Logout :",JSON.stringify(resp))
      if (resp.status === "ok") {

        dispatch(hideRecorder());
        // dispatch(logout());
        // await AsyncStorage.clear();

        await clearSecureStorage();
        if (orgName !== null) {
          await setSecureItem("org", orgName);
        }
        // if (storedRecording !== null) {
        //   await setSecureItem("recording", storedRecording);
        // }
        // if (Platform.OS === 'web') {
        //   window.location.href = '/LoginTenant';
        // } else {
        //   navigation.replace("LoginTenant")
        //}

        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  };

  const isSessionExpired = async () => {
    const recordingURI = recorderState.recordingURI;
    const isFloatingRecorderRunning = recorderState.showPauseResume;
    if (isFloatingRecorderRunning || recordingURI != null) {
      return false;
    } else {
      const lastActive = parseInt(await AsyncStorage.getItem("lastActive"), 10) as number;
      // console.log("Last Active :",lastActive ,typeof lastActive)
      const now = Date.now();
      return now - lastActive > WAIT_TIME; // 2 hours
    }
  };

  const updateLastActive = async () => {
    // localStorage.setItem('lastActive', Date.now().toString());
    await AsyncStorage.setItem('lastActive', Date.now().toString())
  };
  return {
    logOutUser,
    isSessionExpired,
    updateLastActive,
  };
};



