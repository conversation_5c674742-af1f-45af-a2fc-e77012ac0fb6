import React, { useEffect } from "react";
import { createStackNavigator } from "@react-navigation/stack";
import SplashScreen from "src/screens/splash/SplashScreen";
import LoginScreen from "src/screens/login/LoginScreen";
import ForgotPasswordScreen from "src/screens/forgot-password/ForgotPassword";
import Dashboard from "src/screens/dashboard/Dashboard";
import AdminDashboard from "src/features/admin-dashboard/screens/AdminDashboard";
import FormTemplatesScreen from "src/features/admin-dashboard/screens/FormTemplatesScreen";
import FormBuilderScreen from "src/features/form-builder/screens/FormBuilderScreen";
import RecordingScreen from "src/features/recorder/screens/RecordingScreen";
import VisitDetailsScreen from "src/features/visit-details/screens/VisitDetailsScreen";
import OpenVisits from "src/features/visits/screens/OpenVisits";
import ReviewAssessment from "src/features/assessment/ReviewAssessment";
import AccessRestrictionScreen from "src/screens/access-restriction-screen/AccessRestrictionScreen";
import ResetPasswordScreen from "src/screens/reset-password/ResetPasswordScreen";
import CreateStaffScreen from "src/features/create-staff/CreateStaffScreen";
import ClinicianDetailsScreen from "src/features/admin-dashboard/screens/ClinicianDetailsScreen";
import AdminVisitDetailsScreen from "src/features/admin-dashboard/screens/AdminVisitDetailsScreen";
import PatientDetailsScreen from "src/features/admin-dashboard/screens/PatientDetailsScreen";
import AllVisits from "../features/visits/screens/AllVisits";
import RecordingProgressScreen from "../features/floating-recorder/RecorderWithQues";
import HearingAssessment from "../features/floating-recorder/HearingAssessment";
import VisitScreen from "../features/visit-details/screens/VisitScreen";
import MyAccount from '../features/profile/screens/MyAccount';
import VerificationPassword from "../screens/forgot-password/OtpVerification";
import SetNewPassword from "../screens/forgot-password/ConfirmPassword";
import DailyVisitsScreen from "../features/visits/screens/DailyVisitScreen";
import AuthGuard from "src/components/auth/AuthGuard";
import { Platform } from "react-native";
import UsersTableLayout from "../features/admin-dashboard/screens/ClinicianScreen";
import LoginTenantScreen from "../screens/login/LoginTenantScreen";

const Stack = createStackNavigator();


// Custom hook to set the document title on web platforms
const useWebPageTitle = (routeName) => {
  useEffect(() => {
    // console.log("-------")
    if (Platform.OS === 'web') {
      // Get custom title if available, otherwise use route name
      const title =  routeName;
      // console.log(title)
      document.title = title ;
    }
  }, [routeName]);
};

// HOC to add page title functionality
const withPageTitle = (Component, routeName) => {
  return (props) => {
    useWebPageTitle(routeName);
    return <Component {...props} />;
  };
};
const StackNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {/* Public Routes */}
      <Stack.Screen name="Splash" component={SplashScreen} />
      <Stack.Screen 
        name="Login" 
        component={Platform.OS === 'web' ? withPageTitle(LoginTenantScreen, 'Login | Goodly AI') : LoginTenantScreen}
      />
      <Stack.Screen 
        name="LoginTenant" 
        component={Platform.OS === 'web' ? withPageTitle(LoginScreen, 'Login | Goodly AI') : LoginScreen}
      />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
      
      <Stack.Screen name="Verification" component={VerificationPassword} />
      <Stack.Screen name="SetPassword" component={SetNewPassword} />

      {/* Protected Routes - Wrapped with AuthGuard */}
      <Stack.Screen name="Dashboard">
        {(props) => (
          <AuthGuard>
            <Dashboard {...props} />
            {/* {Platform.OS === 'web' ? 
              React.createElement(withPageTitle(Dashboard, 'Home | Scribble'), props) :
              <Dashboard {...props} />
            } */}
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="FormCreation">
        {(props) => (
          <AuthGuard>
            <FormBuilderScreen {...props} />
            
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="AdminDashboard">
        {(props) => (
          <AuthGuard>
            <AdminDashboard {...props} />
            
          </AuthGuard>
        )}
      </Stack.Screen>
      <Stack.Screen name="Clinician">
        {(props) => (
          <AuthGuard>
            <UsersTableLayout {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="FormTemplates">
        {(props) => (
          <AuthGuard>
            <FormTemplatesScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="Recorder">
        {(props) => (
          <AuthGuard>
            <RecordingScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="VisitDetails">
        {(props) => (
          <AuthGuard>
            <VisitDetailsScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="OpenVisits">
        {(props) => (
          <AuthGuard>
            <OpenVisits {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="AllVisits">
        {(props) => (
          <AuthGuard>
            <AllVisits {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="VisitScreen">
        {(props) => (
          <AuthGuard>
            <VisitScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="ReviewAssessment">
        {(props) => (
          <AuthGuard>
            <ReviewAssessment {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="CreateStaffScreen">
        {(props) => (
          <AuthGuard>
            <CreateStaffScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="Assesment">
        {(props) => (
          <AuthGuard>
            <HearingAssessment {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="Recording">
        {(props) => (
          <AuthGuard>
            <RecordingProgressScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="AdminVisitDetails">
        {(props) => (
          <AuthGuard>
            <AdminVisitDetailsScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="PatientDetails">
        {(props) => (
          <AuthGuard>
            <PatientDetailsScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="ClinicianDetails">
        {(props) => (
          <AuthGuard>
            <ClinicianDetailsScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="AccessRestrictionScreen">
        {(props) => (
          <AuthGuard>
            <AccessRestrictionScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="MyAccount">
        {(props) => (
          <AuthGuard>
            <MyAccount {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>

      <Stack.Screen name="DailyVisitsScreen">
        {(props) => (
          <AuthGuard>
            <DailyVisitsScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>
    </Stack.Navigator>
  );
};

export default StackNavigator;