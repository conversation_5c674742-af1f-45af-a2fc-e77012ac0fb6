import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import { ChevronLeft } from 'lucide-react-native';
import { globalStyles } from 'src/styles';

const MobileHeader = ({
  title,
  onBack,
}: {
  title: string;
  onBack: () => void;
}) => {
  return (
    <View style={styles.header}>
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        {/* <ChevronLeft size={30} /> */}
      </TouchableOpacity>
      <View style={styles.titleContainer}>
        <Text style={styles.title}> {title}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    position: 'relative',
    height: 65,
    ...globalStyles.shadow,
  },
  backButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {
    marginLeft: 16,
    gap: 2,
  },
  title: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
  },
  subTitle: {
    fontFamily: 'Poppins_500Medium',
    fontSize: 14,
  },
});

export default MobileHeader;
