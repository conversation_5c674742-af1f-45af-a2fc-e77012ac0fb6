import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
// import { Bell } from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLogin } from '@/src/context/LoginContext';
import { getSecureItem } from '@/src/utils/cryptoHelper';
import { fs } from '@/src/utils/ScreenUtils';

const GreetingHeader =  () => {
  const [greeting, setGreeting] = useState('');
  const [message, setMessage] = useState('');
  const [name,setName] =useState<any>('')
  // const {firstName} =useLogin()
  useEffect(()=>{
    fetchName()
  },[])
  const fetchName= async()=>{
    let firstname=  await getSecureItem('firstName');
    // console.log(firstname)
    setName(firstname)
  }
  // const name="text"
  // const name = await AsyncStorage.getItem("authToken");
  useEffect(() => {
    const getCurrentGreeting = () => {
      const hours = new Date().getHours();
      if (hours < 12) return 'Good Morning';
      if (hours < 18) return 'Good Afternoon';
      if (hours < 21) return 'Good Evening';
      return 'Good Night';
    };

    const getCurrentMessage = () => {
      const hours = new Date().getHours();
      if (hours < 12) return 'Have a wonderful day ahead! ☀️';
      if (hours < 18) return 'Hope you’re having a great afternoon! 🌤️';
      if (hours < 21) return 'Enjoy your evening! 🌇';
      return 'Sleep well and recharge! 🌙';
    };

    setGreeting(getCurrentGreeting());
    setMessage(getCurrentMessage());
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.greetingContainer}>
          <Text allowFontScaling={false} style={styles.greetingText}>
            {greeting}, <Text style={styles.nameText}>{name}</Text> 👋
          </Text>
          {/* <Text style={styles.nameText}>{name}</Text> */}
          {/* <Text style={styles.messageText}>{message}</Text> */}
        </View>

        {/*<TouchableOpacity style={styles.notificationIcon}>*/}
        {/*  <Bell size={28} color={'#4B5563'} />*/}
        {/*  {hasNotifications && <View style={styles.notificationBadge} />}*/}
        {/*</TouchableOpacity>*/}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingContainer: {
    flex: 1,
  },
  greetingText: {
    fontSize: fs(22),
    color: '#1E293B',
    fontFamily: 'Poppins_400Regular',
  },
  nameText: {
    fontSize: fs(18),
    color: '#1E293B',
    fontFamily: 'Poppins_600SemiBold',
  },
  messageText: {
    fontSize: 14,
    color: '#64748B',
    marginTop: 4,
    fontFamily: 'Poppins_400Regular',
  },
  notificationIcon: {
    position: 'relative',
    padding: 10,
  },
  notificationBadge: {
    position: 'absolute',
    top: 10,
    right: 12,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EF4444',
  },
});

export default GreetingHeader;
