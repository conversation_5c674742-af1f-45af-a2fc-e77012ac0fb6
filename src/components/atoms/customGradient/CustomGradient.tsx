import React from "react";
import { StyleSheet, ViewStyle, StyleProp, View } from "react-native";

interface RGBColor {
  r: number;
  g: number;
  b: number;
}

interface CustomGradientProps {
  colors?: string[];
  direction?: "vertical" | "horizontal";
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
}

/**
 * A custom gradient component that creates a smooth linear gradient
 * by using many overlapping views
 */
const CustomColorGradient: React.FC<CustomGradientProps> = ({
  colors = ["#FFFFFF", "#000000"], // Default to white to black
  direction = "vertical",
  style,
  children,
}) => {
  // Default to at least 2 colors
  const gradientColors =
    colors.length >= 2
      ? colors
      : ["#FFFFFF", "#000000"];

  // Create a much higher number of layers for smoother gradients
  const numLayers = 100; // Significantly more layers for smoother gradient
  const layers: JSX.Element[] = [];

  for (let i = 0; i < numLayers; i++) {
    // Calculate the normalized position (0 to 1)
    const position = i / (numLayers - 1);
    
    // Get the interpolated color at this position
    const backgroundColor = getColorAtPosition(gradientColors, position);
    
    // Create object for layer styles
    const layerStyle: ViewStyle = {
      position: "absolute",
      backgroundColor,
    };

    if (direction === "horizontal") {
      // For horizontal gradients
      Object.assign(layerStyle, {
        top: 0,
        bottom: 0,
        left: `${position * 100}%`,
        width: `${120 / numLayers}%`, // Slight overlap between layers helps blend
      });
    } else {
      // For vertical gradients
      Object.assign(layerStyle, {
        left: 0,
        right: 0,
        top: `${position * 100}%`,
        height: `${120 / numLayers}%`, // Slight overlap
      });
    }

    layers.push(<View key={i} style={layerStyle} />);
  }

  return (
    <View style={[styles.container, style]}>
      {layers}
      <View style={styles.content}>{children}</View>
    </View>
  );
};

/**
 * Gets the interpolated color at a specific position in the gradient
 * @param colors Array of hex color strings
 * @param position Position from 0 to 1
 * @returns RGBA color string
 */
const getColorAtPosition = (colors: string[], position: number): string => {
  if (position <= 0) return colors[0];
  if (position >= 1) return colors[colors.length - 1];

  // Calculate which segment the position falls into
  const segment = position * (colors.length - 1);
  const index = Math.floor(segment);
  const segmentPosition = segment - index; // Position within segment (0 to 1)

  // Get the colors to interpolate between
  const color1 = hexToRgb(colors[index]);
  const color2 = hexToRgb(colors[Math.min(index + 1, colors.length - 1)]);

  // Interpolate between the two colors
  const r = Math.round(color1.r + segmentPosition * (color2.r - color1.r));
  const g = Math.round(color1.g + segmentPosition * (color2.g - color1.g));
  const b = Math.round(color1.b + segmentPosition * (color2.b - color1.b));

  return `rgba(${r}, ${g}, ${b}, 1)`;
};

// Helper function to convert hex to rgb
const hexToRgb = (hex: string): RGBColor => {
  // Remove # if present
  hex = hex.replace("#", "");

  // Handle shorthand hex
  if (hex.length === 3) {
    hex = hex
      .split("")
      .map((c) => c + c)
      .join("");
  }

  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    overflow: "hidden",
  },
  content: {
    position: "relative",
    zIndex: 1,
  },
});

export default CustomColorGradient;