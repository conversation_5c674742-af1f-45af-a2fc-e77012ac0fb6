import React, { useState, ReactNode, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  Dimensions,
  GestureResponderEvent,
  ViewStyle,
  TextStyle,
  Pressable,
  Platform,
  Alert,
} from 'react-native';
const { width } = Dimensions.get('window');

interface CustomTooltipProps {
  children: ReactNode;
  content: string[];
  title?: string;
  tooltipWidth?: number;
  tooltipStyle?: ViewStyle;
  textStyle?: TextStyle;
  titleStyle?: TextStyle;
  headerStyle?: ViewStyle;
  contentStyle?: ViewStyle;
  offsetY?: number;
  customDesign?: ReactNode;
  closeIcon?: ReactNode;
  caretSize?: number;
  caretColor?: string;
  onError?: (error: Error) => void;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  children,
  content,
  title = "Context",
  tooltipWidth = width - 20,
  tooltipStyle,
  textStyle,
  titleStyle,
  headerStyle,
  contentStyle,
  offsetY = 20,
  customDesign,
  closeIcon,
  caretSize = 12,
  caretColor,
  onError,
}) => {
  const [visible, setVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [showAbove, setShowAbove] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Validate props on mount and when they change
  useEffect(() => {
    // console.log("Tooltip Content:",content)
    try {
      // Check that content is provided and is an array
      if (!content || !Array.isArray(content)) {
        throw new Error('Content must be provided as an array of strings');
      }
      
      // Check tooltip width is valid
      if (tooltipWidth <= 0) {
        throw new Error('tooltipWidth must be a positive number');
      }
      
      // Check caretSize is valid
      if (caretSize < 0) {
        throw new Error('caretSize must be a non-negative number');
      }
      
      // Clear any previous errors if validation passes
      setError(null);
    } catch (err) {
      // const error = err instanceof Error ? err : new Error('Unknown error in CustomTooltip');
      // setError(error);
      // if (onError) {
      //   onError(error);
      // } else {
      //   console.error('CustomTooltip Error:', error.message);
      // }
    }
  }, [content, tooltipWidth, caretSize, onError]);

  const handlePress = (event: GestureResponderEvent) => {
    try {

      console.log("Tooltip Used:");
      // Get tap position
      const { pageX, pageY } = event.nativeEvent;
      
      if (pageX < 0 || pageY < 0) {
        throw new Error('Invalid touch coordinates detected');
      }
      
      // Set position and show tooltip
      setPosition({ x: pageX, y: pageY });
      
      // Calculate if tooltip should show above or below
      const screenHeight = Dimensions.get('window').height;
      const estimatedHeight = 150; // Estimate tooltip height
      
      if (!screenHeight) {
        throw new Error('Could not determine screen dimensions');
      }
      
      const willShowAbove = pageY + offsetY + estimatedHeight > screenHeight;
      setShowAbove(willShowAbove);
      
      setVisible(true);
    } catch (err) {
      // const error = err instanceof Error ? err : new Error('Unknown error handling press');
      // setError(error);
      // if (onError) {
      //   onError(error);
      // } else {
      //   console.error('CustomTooltip Error:', error.message);
      //   Alert.alert('Tooltip Error', 'Could not display tooltip properly. Please try again.');
      // }
    }
  };

  const closeTooltip = () => {
    try {
      setVisible(false);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Error closing tooltip');
      if (onError) {
        onError(error);
      } else {
        console.error('CustomTooltip Error:', error.message);
      }
    }
  };

  // Safely calculate screen dimensions
  let screenWidth = width;
  try {
    screenWidth = Dimensions.get('window').width;
    if (!screenWidth || screenWidth <= 0) {
      throw new Error('Invalid screen width');
    }
  } catch (err) {
    const error = err instanceof Error ? err : new Error('Error getting screen dimensions');
    if (!error) {
      // Use default width as fallback
      screenWidth = width || 320; // Fallback to a reasonable default
    }
  }
  
  // Horizontal positioning with safety checks
  const tooltipX = position.x + tooltipWidth > screenWidth
    ? Math.max(0, screenWidth - tooltipWidth - 10)
    : Math.max(0, position.x);
  
  // Vertical positioning with safety checks
  const tooltipY = showAbove
    ? Math.max(0, position.y - caretSize - 150) // Show above touch point (with caret size buffer)
    : Math.max(0, position.y + caretSize); // Show below touch point (with caret size buffer)

  // Calculate caret position with bounds checking
  const caretLeft = Math.max(
    Math.min(position.x - tooltipX + 10, tooltipWidth - caretSize * 2),
    caretSize
  );

  // Safely determine background color
  let tooltipBackgroundColor;
  try {
    tooltipBackgroundColor = caretColor || 
      (tooltipStyle && (tooltipStyle.backgroundColor as string)) || 
      '#FFFFFF';
  } catch (err) {
    tooltipBackgroundColor = '#FFFFFF'; // Fallback color
  }

  // If there's an error in the props that would prevent proper rendering, show fallback UI
  if (error) {
    return (
      <View>
        <Pressable>
          {children}
        </Pressable>
      </View>
    );
  }

  return (
    <View>
      <Pressable onPress={handlePress}>
        {children}
      </Pressable>
      
      <Modal 
        transparent 
        visible={visible} 
        animationType="fade" 
        onRequestClose={closeTooltip}
        supportedOrientations={['portrait', 'landscape']}
      >
        <Pressable style={styles.modalOverlay} onPress={closeTooltip}>
          <Pressable 
            onPress={(e) => {
              try {
                e.stopPropagation();
              } catch (err) {
                console.error('Error in event handling:', err);
              }
            }}
          >
            {customDesign ? (
              // Render custom design if provided
              <View
                style={{
                  position: 'absolute',
                  left: tooltipX,
                  top: tooltipY,
                  width: tooltipWidth,
                }}
              >
                {customDesign}
              </View>
            ) : (
              // Render default design with caret
              <View
                style={{
                  position: 'absolute',
                  left: tooltipX,
                  top: tooltipY,
                  width: tooltipWidth,
                }}
              >
                {/* Main tooltip container with built-in top caret */}
                <View
                  style={[
                    styles.tooltip,
                    tooltipStyle,
                  ]}
                >
                  {/* Top caret/arrow pointing up */}
                  <View style={styles.topCaretContainer}>
                    <View style={[
                      styles.topCaret,
                      caretColor ? { borderBottomColor: caretColor } : null
                    ]} />
                  </View>
                
                  {/* Header with title and close button */}
                  <View style={[styles.tooltipHeader, headerStyle]}>
                    <Text 
                      style={[styles.tooltipTitle, titleStyle]}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {title || "Context"}
                    </Text>
                    <Pressable onPress={closeTooltip} hitSlop={10}>
                      {closeIcon || (
                        <Text style={styles.closeIcon}>×</Text>
                      )}
                    </Pressable>
                  </View>
                  
                  {/* Content area */}
                  <View style={[styles.tooltipContent, contentStyle]}>
                    {content && Array.isArray(content) ? (
                      content.map((con: string, index: number) => {
                        return (
                          <Text 
                            key={`tooltip-content-${index}`} 
                            style={[styles.tooltipText, textStyle]}
                          >
                            {con || ""}
                          </Text>
                        );
                      })
                    ) : (
                      <Text style={[styles.tooltipText, textStyle]}>No content available</Text>
                    )}
                  </View>
                </View>
              </View>
            )}
          </Pressable>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  tooltip: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'visible', // Changed to visible to allow the caret to appear outside
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    position: 'relative', // Needed for absolute positioning of caret
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  // Container for the top caret
  topCaretContainer: {
    position: 'absolute',
    top: -10, // Position it above the tooltip
    right: 10, // Position it near the right edge (opposite of the trigger)
    width: 20,
    height: 10,
    overflow: 'visible',
    zIndex: 10,
  },
  // Triangle pointing up at the top of the tooltip
  topCaret: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderBottomWidth: 10,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#FFFFFF', // Same as tooltip background
    zIndex: 2,
  },
  caret: {
    position: 'absolute',
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    zIndex: 1,
  },
  tooltipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 5,
  },
  tooltipTitle: {
    fontSize: 14,
    color: '#251F38',
    fontFamily: 'Poppins_500Medium',
    flex: 1,
    marginRight: 10,
  },
  closeIcon: {
    fontSize: 22,
    color: '#000000',
    fontWeight: '300',
  },
  tooltipContent: {
    paddingTop: 10,
    paddingBottom: 16,
    paddingStart: 16,
    paddingEnd: 16,
  },
  tooltipText: {
    fontSize: 12,
    color: '#251F38',
    lineHeight: 20,
    fontFamily: 'Poppins_400Regular',
  },
});

export default CustomTooltip;