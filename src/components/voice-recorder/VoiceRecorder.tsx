import React, { useContext, useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import VoiceRecorderContext from 'src/context/VoiceRecorderProvider';

export default function VoiceRecorder() {
  const { startRecording, stopRecording, playRecording, recordedUri } =
    useContext(VoiceRecorderContext);
  const [isRecording, setIsRecording] = useState(false);

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
    setIsRecording(!isRecording);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.button} onPress={toggleRecording}>
        <Text style={styles.text}>{isRecording ? 'Stop' : 'Record'}</Text>
      </TouchableOpacity>

      {recordedUri && (
        <TouchableOpacity style={styles.playButton} onPress={playRecording}>
          <Text style={styles.text}>Play</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 50,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    backgroundColor: 'red',
    padding: 15,
    borderRadius: 30,
    alignItems: 'center',
    marginRight: 10,
  },
  playButton: {
    backgroundColor: 'green',
    padding: 15,
    borderRadius: 30,
    alignItems: 'center',
  },
  text: {
    color: 'white',
    fontWeight: 'bold',
  },
});
