import { useEffect } from "react";
import { Platform } from "react-native";

const generateID = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

export const setPageTitle = (title: string) => {
  // useEffect(() => {
    // Only run on web platform
    // console.log(title)
    if (Platform.OS === 'web') {
      // Set the document title
      setTimeout(() => {
        document.title = title || 'Goodly AI ';
      }, 100)
      // Cleanup function to reset title when component unmounts
      return () => {
        document.title = 'Goodly AI ';
      };
    }
  // }, []);
};
export { generateID };
