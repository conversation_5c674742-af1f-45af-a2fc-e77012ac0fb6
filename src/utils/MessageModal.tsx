import React from 'react';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
  Platform
} from 'react-native';
import { useResponsive } from '../hooks/useResponsive';
import theme from '../theme';

const { width } = Dimensions.get('window');

interface DynamicMessageModalProps {
  visible: boolean;
  onCancel: () => void;
  message: string;
  title?: string;        
  iconComponent?: React.ReactNode;  
}
interface DynamicMessageModalWithConfirmProps {
  visible: boolean;
  onCancel: () => void;
  message: string;
  title?: string;        
  iconComponent?: React.ReactNode;  
  onConfirm?:()=>void;
  cancelButtonText?: string;
}
export const MessageModal = ({ visible, onCancel, message, type }) => {
  const { isTabletOrMobileDevice } = useResponsive();
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={confModalStyle.overlay}>
        <View style={[
          confModalStyle.modalContainer,
          {
            width: !isTabletOrMobileDevice ? width * 0.26 : width * 0.95,
          },
        ]}>
          <View style={confModalStyle.iconContainer}>
            {type == "success" ?
              <Ionicons name="checkmark-circle" size={48} color="green" />
              : <Ionicons name="close-circle" size={48} color="red" />
            }
          </View>
          {/* <Text style={styles.title}>Submit Recording?</Text> */}
          <Text style={confModalStyle.message}>
            {message}
          </Text>
          <View style={confModalStyle.buttonContainer}>
            <TouchableOpacity
              style={[
                confModalStyle.cancelButton,
                type != "success" && confModalStyle.redCancelButton
              ]}
              onPress={onCancel}
            >
              <Text style={confModalStyle.cancelButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export const DynamicMessageModal: React.FC<DynamicMessageModalProps> = ({
  visible,
  onCancel,
  message,
  title,
  iconComponent
}) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={deleteRecordStyle.overlay}>
        <View style={deleteRecordStyle.modalContainer}>
          <View style={deleteRecordStyle.iconContainer}>
            {iconComponent &&
              <View style={deleteRecordStyle.icon}>
                {iconComponent}
                {/* <MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" /> */}
                {/* <Image source={require('assets/images/restart.svg')} /> */}
              </View>
            }
          </View>
          {title &&
            <Text style={deleteRecordStyle.title}>{title}</Text>
          }
          <Text style={deleteRecordStyle.message}>
            {message}
          </Text>
          <View style={deleteRecordStyle.buttonContainer}>
            <TouchableOpacity style={deleteRecordStyle.confirmButton} onPress={onCancel}>
              <Text style={deleteRecordStyle.confirmButtonText}>Close</Text>
            </TouchableOpacity>

          </View>
        </View>
      </View>
    </Modal>
  );
};
export const DynamicMessageModalWithConfirm: React.FC<DynamicMessageModalWithConfirmProps> = ({
  visible,
  onCancel,
  message,
  title,
  iconComponent,
  onConfirm,
  cancelButtonText = "Cancel",
}) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={commonRecordStyle.overlay}>
        <View style={commonRecordStyle.modalContainer}>
          <View style={commonRecordStyle.iconContainer}>
            {iconComponent &&
            <View style={commonRecordStyle.icon}>
              {iconComponent}
              
            </View>
}
          </View>
          {title && 
          <Text style={commonRecordStyle.title}>{title}</Text>
}
          <Text style={commonRecordStyle.message}>
            {message}
          </Text>
          <View style={commonRecordStyle.buttonContainer}>
            <TouchableOpacity style={commonRecordStyle.cancelButton} onPress={onCancel}>
              <Text style={commonRecordStyle.cancelButtonText}>{cancelButtonText}</Text>
            </TouchableOpacity>
            {onConfirm &&
            <TouchableOpacity style={commonRecordStyle.confirmButton} onPress={onConfirm}>
              <Text style={commonRecordStyle.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const commonRecordStyle = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.95,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 25,
  },
  title: {
    color: '#000',
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 24,
    fontStyle: 'normal',
  },
  message: {
    color: '#7C7887',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: '400',
    marginTop: 10,
    marginBottom: 15
  }, icon: {
    width: 60,
    height: 60,
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
})
const confModalStyle = StyleSheet.create({
  iconContainer: {
    marginBottom: 12,
  },
  message: {
    color: "#7C7887",
    textAlign: "center",
    fontFamily: "Poppins_400Regular",
    fontSize: 18,
    fontStyle: "normal",
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    // width:Platform.OS === "web" ? (width * 0.26) : (width * 0.85),
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  title: {
    color: "#000",
    textAlign: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 24,
    fontStyle: "normal",
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15
  },
  cancelButton: {
    width: '50%',  // Medium sized button (half of container width)
    backgroundColor: 'green',  // Default gray color
    paddingVertical: 12,
    borderRadius: 24,
    alignItems: 'center',
  },
  redCancelButton: {
    backgroundColor: '#FF0000',  // Red color when type is not error
  },
  cancelButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium'
  },
});
const deleteRecordStyle = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.95,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 25,
  },
  title: {
    color: '#000',
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 24,
    fontStyle: 'normal',
  },
  message: {
    color: '#7C7887',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: '400',
    marginTop: 10,
    marginBottom: 15
  }, icon: {
    width: 60,
    height: 60,
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '75%',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
})

