const getInitials = (name: string) => {
  if (!name) return '';

  return name
    .split(' ') // Split by space
    .filter(word => word.length > 0) // Remove empty strings
    .slice(0, 2) // Take the first two words
    .map(word => word[0].toUpperCase()) // Get the first letter and capitalize
    .join(''); // Join the initials
};

const validateEmail = (email: string) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};
const validateMobile = (mobile: string) => {
  const re = /^\(\d{3}\) \d{3}-\d{4}$/;
  return re.test(mobile);
}
const formatPhoneNumber = (value: string) => {
  // Strip all non-numeric characters
  const phoneNumber = value.replace(/\D/g, '');

  // Format based on input length
  if (phoneNumber.length < 4) {
    return phoneNumber;
  } else if (phoneNumber.length < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  } else {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
};

export { getInitials, validateEmail, validateMobile, formatPhoneNumber };
