import { format, isToday, isYesterday, differenceInDays } from 'date-fns';

function getFormattedDate() {
  const today = new Date();
  const options = {
    weekday: 'long',
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  };
  return today.toLocaleDateString('en-GB', options).replace(',', '');
}

export { getFormattedDate };

export const dateConvert = (inputDate: string, type: number) => {
  // const [month, day, year] = inputDate.split("/").map(Number);
  // const date = new Date(year, month - 1, day);
  const date = new Date(inputDate);

  // For type 3, ensure we use UTC date to avoid timezone issues
  const getUTCDate = () => {
    const utcDate = new Date(inputDate);
    return {
      month: utcDate.getUTCMonth(),
      day: utcDate.getUTCDate(),
      year: utcDate.getUTCFullYear()
    };
  };
  let options = null;
  if (type == 1) {
    // options = { weekday: 'long', day: '2-digit', month: 'short', year: 'numeric' };
    const weekdayStr = date.toLocaleString('en-US', { weekday: 'long' });
    const dayStr = date.getDate().toString().padStart(2, '0');
    const monthStr = date.toLocaleString('en-US', { month: 'short' });
    const yearStr = date.getFullYear();

    return `${weekdayStr}, ${monthStr} ${dayStr}  ${yearStr}`;
  } else if (type == 2) {
    options = { day: '2-digit', month: 'short', year: 'numeric' };
    const formattedDate = date.toLocaleDateString('en-GB', options);
    return formattedDate
  } else if (type == 3) {
    // Use UTC date to avoid timezone issues
    const { month, day, year } = getUTCDate();

    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const monthStr = monthNames[month];
    const dayStr = day.toString().padStart(2, '0');

    return `${monthStr} ${dayStr}, ${year}`;
  } else {
    let hr = date.getHours();
    let mn = date.getMinutes();
    let ampm = hr >= 12 ? 'PM' : 'AM';

    let hour = hr.toString().padStart(2, '0');
    let minute = mn.toString().padStart(2, '0');

    return `${hour}:${minute} ${ampm}`;
  }

}

export const formatDateDifference = (dateString: string): string => {
  const date = new Date(dateString);
  const currentDate = new Date();

  if (isToday(date)) {
    return `Today ${format(date, 'h:mm a')}`;
  } else if (isYesterday(date)) {
    return `Yesterday ${format(date, 'h:mm a')}`;
  } else {
    const diffDays = differenceInDays(currentDate, date);
    return `${diffDays} days ago ${format(date, 'h:mm a')}`;
  }
};
export const formatDateDifference_v1 = (dateString: string): string => {
  const date = new Date(dateString);
  const currentDate = new Date();

  if (isToday(date)) {
    return `Today `;
  } else if (isYesterday(date)) {
    return `Yesterday `;
  } else {
    // const diffDays = differenceInDays(currentDate, date);
    const [month, day, year] = dateString.split("/").map(Number);
    const date = new Date(year, month - 1, day);

    const options = { day: '2-digit', month: 'short', year: 'numeric' };
    const formattedDate = date.toLocaleDateString('en-GB', options);
    return formattedDate;
  }
};
export const formatLocalTimeFromGMT = (gmtDateString: string): string => {
  const date = new Date(gmtDateString);
  let hr = date.getHours();
  let mn = date.getMinutes();
  let ampm = hr >= 12 ? 'PM' : 'AM';
  let hour = (hr % 12) || 12;
  let hourStr = hour.toString().padStart(2, '0');
  let minuteStr = mn.toString().padStart(2, '0');
  return `${hourStr}:${minuteStr} ${ampm}`;
};
