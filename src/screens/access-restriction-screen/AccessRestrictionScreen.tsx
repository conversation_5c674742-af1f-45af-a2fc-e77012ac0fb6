import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Image, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from 'src/redux/store';
import { useResponsive } from 'src/hooks/useResponsive';
import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';
import { logout } from '@/src/redux/slices/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';


const AccessRestrictionScreen = ({ navigation }: { navigation: any }) => {
  const dispatch = useDispatch();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const user = useSelector((state: RootState) => state.auth.user); // "clinician" or "admin"

  const { isTabletOrMobileDevice } = useResponsive();

  const styles = accessRestrictionScreenStyles(isTabletOrMobileDevice);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const logOut = async () => {
    dispatch(logout());
    await AsyncStorage.clear()
    navigation.replace('Login');
  }
  return (
    <View style={styles.rootContainer}>
      <LinearGradient
        colors={['#4C51BF', '#6B46C1']} // from indigo-600 to purple-700
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          {!isTabletOrMobileDevice && (
            <Image
              source={require('assets/images/clinician-mobile.png')}
              style={styles.logo}
            />
          )}
          {!isTabletOrMobileDevice ? (
            <View style={styles.textContainer}>
              <Image
                source={require('assets/images/splash-icon.png')}
                style={styles.icon}
              />
              <Text style={styles.text}>
                This application is only accessible on mobile devices for
                clinicians.
              </Text>
              <View style={styles.secondLineContainer}>
                <Text style={styles.text}>
                  Please use a
                  <Text style={styles.assistantText}>
                    {` smartphone or tablet `}
                  </Text>
                  to continue.
                </Text>
              </View>
              <View style={styles.secondLineContainer}>
                <TouchableOpacity style={styles.buttonContainer} onPress={logOut}>
                  <Text style={styles.buttonText} >Log Out</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.textContainer}>
              <Image
                source={require('assets/images/splash-icon.png')}
                style={styles.icon}
              />
              <Text style={styles.text}>
                This admin panel is only accessible from a desktop or laptop.
              </Text>
              <View style={styles.secondLineContainer}>
                <Text style={styles.text}>
                  Please use a
                  <Text style={styles.assistantText}>
                    {` web browser in desktop or laptop `}
                  </Text>
                  to continue.
                </Text>
              </View>
              <View style={styles.secondLineContainer}>
                <TouchableOpacity style={styles.buttonContainer} onPress={logOut}>
                  <Text style={styles.buttonText} >Log Out</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

        </Animated.View>
      </LinearGradient>
    </View>
  );
};

export default AccessRestrictionScreen;

const accessRestrictionScreenStyles = (isMobile: boolean) =>
  StyleSheet.create({
    rootContainer: {
      flex: 1,
      // The gradient covers the full screen; align items in the center
      alignItems: 'center',
      justifyContent: 'center',
    },
    container: {
      width: isMobile ? '100%' : '50%',
      height: isMobile ? '100%' : '50%',
      maxHeight: isMobile ? screenHeight : 432,
      minHeight: isMobile ? screenHeight : 400,
      maxWidth: isMobile ? screenWidth : 780,
      borderRadius: isMobile ? 0 : 16,
      shadowColor: '#000',
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      display: 'flex',
      flexDirection: 'row',
    },
    content: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      flexDirection: 'row',
      position: 'relative',
      width: '100%',
      height: '100%',
      paddingRight: isMobile ? 30 : 60,
      paddingLeft: isMobile ? 30 : 0,
    },
    logo: {
      position: 'absolute',
      width: 320,
      height: 400,
      resizeMode: 'contain',
      bottom: 0,
      left: 40,
    },
    icon: {
      width: 120,
      height: 120,
      resizeMode: 'contain',
      marginBottom: isMobile ? 60 : 30,
    },
    textContainer: {
      alignItems: 'center',
      width: isMobile ? screenWidth - 60 : 330,
    },
    // Using Poppins_700Bold for a large headline (like nameText in HomeScreen)
    text: {
      fontSize: 20,
      color: '#ffffff',
      textAlign: 'center',
      fontFamily: 'Poppins_500Medium',
    },
    // Highlight the word "Assistant" with a color change or possibly different font
    // Here, keep the same family for consistent style, but you can change it if desired
    assistantText: {
      color: '#FFD700',
      fontFamily: 'Poppins_600SemiBold',
    },
    secondLineContainer: {
      alignItems: 'center',
      marginTop: 24,
    },
    buttonContainer: {
      width: 100, height: 40, borderWidth: 2,
      borderColor: '#ddd',
      paddingTop: 7,
      paddingBottom: 7,
      borderRadius: 8, alignItems: 'center'
    },
    buttonText: {
      color: '#ddd',
      fontWeight: '500', fontFamily: 'Poppins_600SemiBold',
      fontSize: 14,
    }
  });
