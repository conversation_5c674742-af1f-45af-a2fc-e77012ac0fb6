import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
// import { Lock } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useResponsive } from 'src/hooks/useResponsive';
import LoginRightPane from 'src/components/login-right-pane/LoginRightPane';

const SetNewPassword = ({ navigation, route }) => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isDisabled, setDisabled] = useState(true);

  const { isTabletOrMobileDevice } = useResponsive();

  const styles = newPasswordStyles(isTabletOrMobileDevice);

  useEffect(() => {
    setErrorMessage('');
    // Enable button only if both passwords are entered and they match
    setDisabled(
      !newPassword || 
      !confirmPassword || 
      newPassword !== confirmPassword ||
      newPassword.length < 6
    );
  }, [newPassword, confirmPassword]);

  const handleSubmit = () => {
    if (newPassword !== confirmPassword) {
      setErrorMessage('Passwords do not match!');
      return;
    }
    
    setIsLoading(true);
    // Here you would call your API to set the new password
    // For example:
    // resetPassword({ password: newPassword, email: route.params?.email, token: route.params?.token })
    //   .then(result => {
    //     setIsLoading(false);
    //     if (result?.status.toLowerCase() === 'ok') {
    //       alert('Password has been reset successfully!');
    //       navigation.replace('Login');
    //     } else {
    //       setErrorMessage('Failed to reset password. Please try again.');
    //     }
    //   })
    //   .catch(error => {
    //     setIsLoading(false);
    //     setErrorMessage('An error occurred. Please try again.');
    //   });
    
    // For now, let's simulate success
    setTimeout(() => {
      setIsLoading(false);
      alert('Password has been reset successfully!');
      navigation.replace('Login');
    }, 1000);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={styles.outerContainer}
    >
      <LinearGradient
        colors={['#e0e7ff', '#ffffff', '#f3e8ff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBG}
      >
        {/* Main container that can hold both left form + right pane if not mobile */}
        <View
          style={[
            styles.container,
            isTabletOrMobileDevice
              ? styles.containerMobile
              : styles.containerDesktop,
          ]}
        >
          {/* Left (login card) */}
          <View style={styles.loginCard}>
            <View style={styles.loginContainer}>
              <View style={styles.iconContainer}>
                <Image
                  source={require('assets/images/icon.png')}
                  style={styles.logo}
                />
              </View>

              {/* Header */}
              <View style={styles.headerContainer}>
                <Text style={styles.title}>Set New Password</Text>
                <Text style={styles.subtitle}>
                  Create a strong password to secure your account
                </Text>
              </View>

              {/* New Password Input */}
              <View style={styles.inputContainer}>
                <View style={styles.iconWrapper}>
                  {/* <Lock color="#9CA3AF" size={20} /> */}
                </View>
                <TextInput
                  style={styles.input}
                  placeholder="New Password"
                  value={newPassword}
                  onChangeText={setNewPassword}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                  placeholderTextColor="grey"
                />
              </View>

              {/* Confirm Password Input */}
              <View style={styles.inputContainer}>
                <View style={styles.iconWrapper}>
                  {/* <Lock color="#9CA3AF" size={20} /> */}
                </View>
                <TextInput
                  style={styles.input}
                  placeholder="Confirm Password"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                  placeholderTextColor="grey"
                />
              </View>

              {/* Submit Button */}
              <TouchableOpacity
                style={[
                  styles.buttonContainer,
                  isDisabled && styles.disabledButton,
                ]}
                activeOpacity={isDisabled ? 0.5 : 0.9}
                onPress={() => !isLoading && handleSubmit()}
                disabled={isDisabled}
              >
                <LinearGradient
                  colors={
                    isDisabled ? ['#A0A0A0', '#C0C0C0'] : ['#4C51BF', '#6B46C1']
                  }
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.buttonGradient}
                >
                  <View style={styles.buttonContent}>
                    <Text style={styles.buttonText}>
                      Reset Password
                    </Text>
                  </View>
                </LinearGradient>
              </TouchableOpacity>

              {errorMessage && (
                <View style={styles.errorContainer}>
                  <Text
                    style={[
                      styles.errorText,
                      isTabletOrMobileDevice
                        ? styles.mobileError
                        : styles.desktopError,
                    ]}
                  >
                    {errorMessage}
                  </Text>
                </View>
              )}
            </View>
            {!isTabletOrMobileDevice && <LoginRightPane />}
          </View>
        </View>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
};

export default SetNewPassword;

const newPasswordStyles = (isMobile) =>
  StyleSheet.create({
    outerContainer: {
      flex: 1,
    },
    gradientBG: {
      flex: 1,
      padding: 16,
      justifyContent: "center",
    },
    container: {
      backgroundColor: "transparent",
      borderRadius: 16,
      overflow: "hidden",
      paddingTop: 10,
      paddingBottom: 10,
    },
    // Layout differences for mobile vs. desktop
    containerMobile: {
      flexDirection: "column",
      alignItems: "center",
    },
    containerDesktop: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
    },
    // The login card (left side)
    loginCard: {
      width: isMobile ? "95%" : "70%",
      borderRadius: 16,
      shadowColor: "#000",
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      display: "flex",
      flexDirection: "row",
    },
    loginContainer: {
      alignSelf: "center",
      maxWidth: isMobile ? "100%" : "100%",
      width: isMobile ? "100%" : "50%",
      padding: isMobile ? 20 : 100,
      paddingVertical: isMobile ? 20 : 30,
      paddingBottom: isMobile ? 30 : 40,
      backgroundColor: "#FFFFFF",
      borderBottomRightRadius: isMobile ? 16 : 0,
      borderTopRightRadius: isMobile ? 16 : 0,
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    },
    iconContainer: {
      display: "flex",
      justifyContent: "center",
      marginBottom: 20,
      alignItems: "center",
    },
    logo: {
      width: isMobile ? 70 : 80,
      height: isMobile ? 70 : 80,
      resizeMode: "contain",
    },
    headerContainer: {
      marginBottom: isMobile ? 24 : 36,
      alignItems: "center",
    },
    title: {
      fontSize: isMobile ? 22 : 24,
      fontFamily: "Poppins_600SemiBold",
      color: "#1F2937",
      marginBottom: isMobile ? 6 : 12,
      textAlign: "center",
    },
    subtitle: {
      fontSize: 14,
      fontFamily: "Poppins_400Regular",
      color: "#4B5563",
      textAlign: "center",
    },
    inputContainer: {
        position: "relative",
        marginBottom: 16,
        flexDirection: "row",
        alignItems: "center",
        width: "100%",
      },
      iconWrapper: {
        position: "absolute",
        left: 14,
        zIndex: 1,
        opacity: 0.7,
      },
      input: {
        flex: 1,
        paddingVertical: 14,
        paddingLeft: 42,
        paddingRight: 16,
        borderWidth: 1,
        borderColor: "#E5E7EB",
        borderRadius: 8,
        fontSize: 16,
        color: "#1F2937",
        backgroundColor: "white",
      },
    buttonContainer: {
      height: isMobile ? 48 : 56,
      width: "100%",
      marginTop: 8,
    },
    disabledButton: {
      opacity: 0.7,
    },
    buttonGradient: {
      borderRadius: 12,
      paddingVertical: 12,
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: isMobile ? 24 : 32,
    },
    buttonText: {
      color: "#FFFFFF",
      fontSize: isMobile ? 14 : 16,
      fontFamily: "Poppins_600SemiBold",
    },
    errorContainer: {
      backgroundColor: "#FEF2F2",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: isMobile ? 8 : 12,
      paddingHorizontal: 30,
      marginTop: 16,
      borderRadius: 4,
      width: "100%",
    },
    errorText: {
      color: "#DC2626",
      fontSize: 14,
      textAlign: "center",
      fontFamily: "Poppins_400Regular",
    },
    mobileError: {
      fontSize: 14,
    },
    desktopError: {
      fontSize: 16,
    },
  });