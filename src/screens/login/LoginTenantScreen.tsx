import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  InputAccessoryView,
  Button,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useResponsive } from "src/hooks/useResponsive";
import LoginRightPane from "src/components/login-right-pane/LoginRightPane";
import { SafeAreaView } from "react-native-safe-area-context";
import { globalStyles } from "src/styles";
import { StatusBar } from "expo-status-bar";

import { Role } from "src/enums/Role";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { setPageTitle } from "@/src/utils/GeneralUtils";
// import { useLogin } from "@/src/context/LoginContext";
import Feather from '@expo/vector-icons/Feather';
import { Octicons } from "@expo/vector-icons";
// import { setSecureItem } from "@/src/utils/cryptoHelper";
import { BUILDING_ICON } from "@/assets/images";
import { getSecureItem, setSecureItem } from "@/src/utils/cryptoHelper";
import { MessageModal } from "@/src/utils/MessageModal";

export default function LoginTenantScreen({ navigation }: { navigation: any }) {

  useEffect(() => {
    setTimeout(() => {
      setPageTitle('Login | Scribble');
    }, 100)
  }, [])
  const [org, setOrg] = useState("");

  const [isDisabled, setDisabled] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");

  const { isTabletOrMobileDevice } = useResponsive();


  const styles = loginScreenStyles(isTabletOrMobileDevice);
  const inputAccessoryViewID = 'orgInputAccessory';
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("error");
  // const {setLoginDetails} =useLogin()
  const onModalCancel = () => setShowMessageModal(false);


  useEffect(() => {
    setErrorMessage("");
    setDisabled(org.trim() === "");
  }, [org]);

  // Check if we're already logged i

  // const handleSubmit = async () => {
  //   // await AsyncStorage.setItem("tenant",org);

  //   await setSecureItem("org", org)
  //   //  await AsyncStorage.setItem("org",org);
  //   // await SecureStore.setItemAsync("org",org);
  //   //console.log("org : ",org)
  //   navigation.replace("LoginTenant")
  // };
  const handleSubmit = async () => {
    const trimmedOrg = org.trim();

    if (trimmedOrg === "") {
      setMessage("Please enter your org ID");
      setMessageType("error");
      setShowMessageModal(true);
      return;
    }

    setErrorMessage("");
    await setSecureItem("org", trimmedOrg);
    navigation.replace("LoginTenant");
  };



  return (
    <SafeAreaView style={globalStyles.flex1}>
      <StatusBar style="Dark" />
      <MessageModal
        visible={showMessageModal}
        onCancel={onModalCancel}
        message={message}
        type={messageType}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={styles.outerContainer}
      >
        <LinearGradient
          colors={["#e0e7ff", "#ffffff", "#f3e8ff"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientBG}
        >
          {/* Main container that can hold both left form + right pane if not mobile */}
          <View
            style={[
              styles.container,
              isTabletOrMobileDevice
                ? styles.containerMobile
                : styles.containerDesktop,
            ]}
          >
            {/* Left (login card) */}
            <View style={styles.loginCard}>
              <View style={styles.loginContainer}>
                <View style={styles.iconContainer}>
                  <Image
                    source={require("assets/images/icon.png")}
                    style={styles.logo}
                  />
                </View>

                <View style={styles.headerContainer}>
                  <Text style={styles.title}>Welcome to Goodly AI</Text>
                  <Text style={styles.subtitle}>
                    {/* Elevate Patient Care with AI */}
                    Patients, Not Paperwork
                  </Text>
                </View>

                {/* Org input */}
                <View style={styles.inputContainer}>
                  <View style={styles.iconWrapper}>
                    {/* <Octicons name="organization" size={24} color="#9CA3AF" /> */}
                    <Image source={BUILDING_ICON} style={{ width: 20, height: 20 }} />
                  </View>
                  {/* <TextInput
                    allowFontScaling={false}
                    style={styles.input}
                    placeholder="Enter your organization name"
                    placeholderTextColor="grey"
                    value={org}
                    onChangeText={setOrg}
                    autoCapitalize="none"
                    autoCorrect={false}
                  /> */}
                  <TextInput
                    allowFontScaling={false}
                    style={styles.input}
                    placeholder="Enter your organization name"
                    placeholderTextColor="grey"
                    value={org}
                    onChangeText={setOrg}
                    autoCapitalize="none"
                    autoCorrect={false}
                    inputAccessoryViewID={Platform.OS === 'ios' ? inputAccessoryViewID : undefined}
                  />
                  {Platform.OS === 'ios' && (
                    <InputAccessoryView nativeID={inputAccessoryViewID}>
                      <View style={{ backgroundColor: '#f1f1f1', padding: 8, alignItems: 'flex-end' }}>
                        <Button onPress={handleSubmit} title="Done" />
                      </View>
                    </InputAccessoryView>
                  )}
                </View>

                <TouchableOpacity
                  style={[
                    styles.buttonContainer,
                    isDisabled && styles.disabledButton,
                  ]}
                  activeOpacity={isDisabled ? 0.5 : 0.9}
                  onPress={() => handleSubmit()}
                  disabled={isDisabled}
                >
                  <LinearGradient
                    colors={
                      isDisabled
                        ? ["#A0A0A0", "#C0C0C0"]
                        : ["#4C51BF", "#6B46C1"]
                    }
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    <View style={styles.buttonContent}>

                      <Text id="login-sign-in-button" style={styles.buttonText}>Continue</Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
                {errorMessage && (
                  <View style={styles.errorContainer}>
                    <Text
                      style={[
                        styles.errorText,
                        isTabletOrMobileDevice
                          ? styles.mobileError
                          : styles.desktopError,
                      ]}
                    >
                      {errorMessage}
                    </Text>
                  </View>
                )}
              </View>
              {!isTabletOrMobileDevice && <LoginRightPane />}
            </View>
          </View>
        </LinearGradient>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const loginScreenStyles = (isMobile: boolean) =>
  StyleSheet.create({
    outerContainer: {
      flex: 1,
    },
    gradientBG: {
      flex: 1,
      padding: 16,
      justifyContent: "center",
    },
    container: {
      backgroundColor: "transparent",
      borderRadius: 16,
      overflow: "hidden",
      paddingTop: 10,
      paddingBottom: 10,
    },
    // Layout differences for mobile vs. desktop
    containerMobile: {
      flexDirection: "column",
      alignItems: "center",
    },
    containerDesktop: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
    },
    // The login card (left side)
    loginCard: {
      width: isMobile ? "95%" : "70%",
      borderRadius: 16,
      shadowColor: "#000",
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      display: "flex",
      flexDirection: "row",
    },
    loginContainer: {
      alignSelf: "center",
      maxWidth: isMobile ? "100%" : "100%",
      width: isMobile ? "100%" : "50%",
      padding: isMobile ? 20 : 100,
      paddingVertical: isMobile ? 20 : 30,
      paddingBottom: isMobile ? 30 : 40,
      backgroundColor: "#FFFFFF",
      borderBottomRightRadius: isMobile ? 16 : 0,
      borderTopRightRadius: isMobile ? 16 : 0,
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    },
    iconContainer: {
      display: "flex",
      justifyContent: "center",
      marginBottom: 20,
      alignItems: "center",
    },
    logo: {
      width: isMobile ? 70 : 80,
      height: isMobile ? 70 : 80,
      resizeMode: "contain",
    },
    headerContainer: {
      marginBottom: isMobile ? 24 : 36,
      alignItems: "center",
    },
    title: {
      fontSize: isMobile ? 18 : 20,
      fontFamily: "Poppins_600SemiBold",
      color: "#1F2937",
      marginBottom: isMobile ? 6 : 12,
    },
    subtitle: {
      fontSize: 14,
      fontFamily: "Poppins_400Regular",
      color: "#4B5563",
    },
    inputContainer: {
      position: "relative",
      marginBottom: 16,
      flexDirection: "row",
      alignItems: "center",
      width: "100%",
    },
    iconWrapper: {
      position: "absolute",
      left: 14,
      zIndex: 1,
      opacity: 0.7,
    },
    input: {
      flex: 1,
      paddingVertical: 14,
      paddingLeft: 42,
      paddingRight: 16,
      borderWidth: 1,
      borderColor: "#E5E7EB",
      borderRadius: 8,
      fontSize: 16,
      color: "#1F2937",
      backgroundColor: "white",
    },
    optionsRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 8,
      marginBottom: 24,
    },
    checkbox: {
      borderRadius: 8,
    },
    rememberMe: {
      flexDirection: "row",
      alignItems: "center",
    },
    rememberMeText: {
      marginLeft: 8,
      fontSize: 14,
      fontFamily: "Poppins_400Regular",
      color: "#4B5563",
    },
    forgotTextContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 20,
    },
    forgotText: {
      fontSize: isMobile ? 14 : 16,
      fontFamily: "Poppins_400Regular",
      color: "#4C51BF",
    },
    buttonContainer: {
      height: isMobile ? 48 : 56,
    },
    disabledButton: {
      cursor: "not-allowed",
    },
    buttonGradient: {
      borderRadius: 12,
      paddingVertical: 12,
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: isMobile ? 24 : 32,
    },
    buttonText: {
      marginLeft: 8,
      color: "#FFFFFF",
      fontSize: isMobile ? 14 : 16,
      fontFamily: "Poppins_600SemiBold",
    },
    errorText: {
      color: "#DC2626",
      fontSize: 14,
      textAlign: "center",
      fontFamily: "Poppins_400Regular",
    },
    mobileError: {
      fontSize: 14,
    },
    desktopError: {
      fontSize: 16,
    },
    errorContainer: {
      backgroundColor: "#FEF2F2",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: isMobile ? 8 : 12,
      paddingHorizontal: 30,
      marginTop: 16,
      borderRadius: 4,
    },
  });
