import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
// import { Bell, MoreVertical, Clock } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

type Appointment = {
  id: string;
  date: string;
  time: string;
  doctor: {
    name: string;
    specialty: string;
    image: string;
  };
};

export default function Assessments() {
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>('upcoming');

  const appointments: Appointment[] = [
    {
      id: '1',
      date: 'Wed Jun 20',
      time: '8:00 - 8:30 AM',
      doctor: {
        name: 'dr. <PERSON><PERSON>',
        specialty: 'Orthopedic',
        image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2',
      },
    },
    {
      id: '2',
      date: 'Thu Jun 21',
      time: '8:00 - 8:30 AM',
      doctor: {
        name: 'dr. <PERSON><PERSON>',
        specialty: 'Obstetrician',
        image: 'https://images.unsplash.com/photo-1537368910025-700350fe46c7',
      },
    },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Schedule</Text>
        <TouchableOpacity style={styles.bellContainer}>
          {/* <Bell size={24} color="#1E293B" /> */}
        </TouchableOpacity>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'upcoming' && styles.activeTab]}
          onPress={() => setActiveTab('upcoming')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'upcoming' && styles.activeTabText,
            ]}
          >
            Upcoming
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'past' && styles.activeTab]}
          onPress={() => setActiveTab('past')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'past' && styles.activeTabText,
            ]}
          >
            Past
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.appointmentsList}>
        {appointments.map((appointment) => (
          <View key={appointment.id} style={styles.appointmentCard}>
            <View style={styles.appointmentBorder} />
            <View style={styles.appointmentContent}>
              <Text style={styles.appointmentLabel}>Appointment date</Text>
              <View style={styles.dateTimeContainer}>
                {/* <Clock size={20} color="#64748B" /> */}
                <Text style={styles.dateTimeText}>
                  {appointment.date} • {appointment.time}
                </Text>
              </View>
              <View style={styles.doctorInfo}>
                <Image
                  source={{ uri: appointment.doctor.image }}
                  style={styles.doctorAvatar}
                />
                <View style={styles.doctorDetails}>
                  <Text style={styles.doctorName}>{appointment.doctor.name}</Text>
                  <Text style={styles.doctorSpecialty}>
                    {appointment.doctor.specialty}
                  </Text>
                </View>
                <TouchableOpacity style={styles.moreButton}>
                  {/* <MoreVertical size={20} color="#64748B" /> */}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Poppins_600SemiBold',
    color: '#1E293B',
  },
  bellContainer: {
    width: 48,
    height: 48,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 100,
    marginRight: 12,
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  tabText: {
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    color: '#64748B',
  },
  activeTabText: {
    color: '#1E293B',
  },
  appointmentsList: {
    paddingHorizontal: 20,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  appointmentBorder: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
    backgroundColor: '#3B82F6',
  },
  appointmentContent: {
    padding: 16,
  },
  appointmentLabel: {
    fontFamily: 'PlusJakartaSans_400Regular',
    fontSize: 14,
    color: '#64748B',
    marginBottom: 8,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  dateTimeText: {
    fontFamily: 'PlusJakartaSans_600SemiBold',
    fontSize: 16,
    color: '#1E293B',
    marginLeft: 8,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doctorAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontFamily: 'PlusJakartaSans_600SemiBold',
    fontSize: 16,
    color: '#1E293B',
  },
  doctorSpecialty: {
    fontFamily: 'PlusJakartaSans_400Regular',
    fontSize: 14,
    color: '#64748B',
  },
  moreButton: {
    padding: 8,
  },
});
