import { combineReducers } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import recordingReducer from './slices/recordingSlice';
import assessmentReducer from './slices/assessmentSlice';
import routeReducer from './slices/routeSlice';
import audioRecorderReducer from './slices/audioRecorderSlice';


const rootReducer = combineReducers({
  auth: authReducer,
  recording: recordingReducer,
  assessment: assessmentReducer,
  route: routeReducer,
  audioRecorder: audioRecorderReducer,

});

export default rootReducer;
