import React, { createContext, useState, useRef } from "react";
import { Audio } from "expo-av";

const VoiceRecorderContext = createContext();

export const VoiceRecorderProvider = ({ children }) => {
  const [recording, setRecording] = useState(null);
  const [recordedUri, setRecordedUri] = useState(null);
  const audioRef = useRef(null);

  const startRecording = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== "granted") {
        alert("Permission to access microphone is required!");
        return;
      }

      const newRecording = new Audio.Recording();
      await newRecording.prepareToRecordAsync(Audio.RECORDING_OPTIONS_PRESET_HIGH_QUALITY);
      await newRecording.startAsync();
      setRecording(newRecording);
    } catch (error) {
      console.error("Failed to start recording", error);
    }
  };

  const stopRecording = async () => {
    try {
      if (recording) {
        await recording.stopAndUnloadAsync();
        const uri = recording.getURI();
        setRecordedUri(uri);
        setRecording(null);
      }
    } catch (error) {
      console.error("Failed to stop recording", error);
    }
  };

  const playRecording = async () => {
    if (recordedUri) {
      if (audioRef.current) {
        await audioRef.current.unloadAsync();
      }

      const { sound } = await Audio.Sound.createAsync({ uri: recordedUri });
      audioRef.current = sound;
      await sound.playAsync();
    }
  };

  return (
    <VoiceRecorderContext.Provider value={{ startRecording, stopRecording, playRecording, recordedUri }}>
  {children}
  </VoiceRecorderContext.Provider>
);
};

export default VoiceRecorderContext;
