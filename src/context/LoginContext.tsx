import React, { createContext, useContext, useState } from "react";
const LoginContext = createContext();

export const LoginProvider = ({ children }) => {
    const [email, setEmail] = useState("");
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');

    const setLoginDetails = (email: string, firstName: string, lastName: string) => {
        setEmail(email);
        setFirstName(firstName);
        setLastName(lastName)
    };
    return (
        <LoginContext.Provider value={{ email, firstName, setLoginDetails, lastName }}>
            {children}
        </LoginContext.Provider>
    );
};
export const useLogin = () => useContext(LoginContext);