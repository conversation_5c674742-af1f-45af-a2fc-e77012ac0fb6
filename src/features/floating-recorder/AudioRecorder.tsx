import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
  Dimensions,
  ActivityIndicator,
  Image,
  Modal,
  StatusBar,
  ScrollView,
  BackHandler,
  Platform,
  Pressable,
  NativeModules,
  NativeEventEmitter,
  Linking
} from 'react-native';
import { Audio } from 'expo-av';
// import {
//   ChevronDown,
//   ChevronUp,
//   X,
// } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from 'src/redux/store';

import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';

const MINI_PLAYER_HEIGHT = 55;
const EXPANDED_HEIGHT = screenHeight;
import { FontAwesome, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');
import { uploadRecord, fetchAssesmentId, fetchFormDetails } from './record-api';
import { useNotification } from '@/src/navigation/NotificationContext';
import ToastService from '@/src/utils/toastService';
import { hideRecorder, resetRecorder, toggleExpanded } from '@/src/redux/slices/audioRecorderSlice';
import { DynamicMessageModal, DynamicMessageModalWithConfirm, MessageModal } from '@/src/utils/MessageModal';
import theme from '@/src/theme';
import { GlobalFunctions } from '@/src/utils/GlobalFunction';
import { Feather } from '@expo/vector-icons';
import { setSecureItem, getSecureItem, removeSecureItem } from '@/src/utils/cryptoHelper';
import { ENABLE_MICROPHONE, ENABLE_MICROPHONE_TITLE, UPLOAD_MINI_ERROR } from '@/src/components/messages';
import { get } from 'react-native/Libraries/TurboModule/TurboModuleRegistry';
const { RecorderModule } = NativeModules;
// import { activateKeepAwake, deactivateKeepAwake } from 'expo-keep-awake';


// Calculate dimensions for fixed elements
const HEADER_HEIGHT = 60;
const PROGRESS_HEIGHT = 50;
const FOOTER_HEIGHT = 110;
const HANDLE_HEIGHT = 5;

// const useWakeLock = () => {
//   const [wakeLock, setWakeLock] = useState(null);
//   const [isSupported, setIsSupported] = useState(false);

//   useEffect(() => {
//     // Check if Wake Lock API is supported
//     if (typeof navigator !== 'undefined' && 'wakeLock' in navigator) {
//       setIsSupported(true);
//     }
//   }, []);

//   const requestWakeLock = async () => {
//     if (!isSupported) {
//       console.log('Wake Lock API not supported');
//       return false;
//     }

//     try {
//       const lock = await navigator.wakeLock.request('screen');
//       setWakeLock(lock);
//       console.log('Wake Lock acquired');

//       // Listen for release events
//       lock.addEventListener('release', () => {
//         console.log('Wake Lock was released');
//         setWakeLock(null);
//       });

//       return true;
//     } catch (err) {
//       console.error(`Failed to acquire wake lock: ${err.message}`);
//       return false;
//     }
//   };

//   const releaseWakeLock = async () => {
//     if (wakeLock) {
//       try {
//         await wakeLock.release();
//         setWakeLock(null);
//         console.log('Wake Lock released');
//       } catch (err) {
//         console.error('Failed to release wake lock:', err);
//       }
//     }
//   };

//   // Auto-reacquire wake lock when page becomes visible again
//   useEffect(() => {
//     if (!isSupported) return;

//     const handleVisibilityChange = async () => {
//       if (document.visibilityState === 'visible' && !wakeLock) {
//         // Only reacquire if we had one before
//         await requestWakeLock();
//       }
//     };

//     document.addEventListener('visibilitychange', handleVisibilityChange);

//     return () => {
//       document.removeEventListener('visibilitychange', handleVisibilityChange);
//     };
//   }, [wakeLock, isSupported]);

//   return { requestWakeLock, releaseWakeLock, isSupported, isActive: !!wakeLock };
// };

const MicrophonePermissionModal = ({ visible, onCancel, onSettings }) => {
  const openSettings = async () => {
    try {
      if (Platform.OS === 'ios') {
        // For iOS, open app-specific settings
        await Linking.openURL('app-settings:');
      } else if (Platform.OS === 'android') {
        // For Android, open app-specific settings
        await Linking.openSettings();
      } else {
        // For web, show browser-specific instructions
        // Alert.alert(
        //   'Enable Microphone',
        //   'Please enable microphone access in your browser settings:\n\n1. Click the microphone icon in the address bar\n2. Select "Allow" for microphone access\n3. Refresh the page if needed',
        //   [{ text: 'OK' }]
        // );
      }
      onSettings?.();
    } catch (error) {
      console.error('Error opening settings:', error);
      Alert.alert('Error', 'Unable to open settings. Please go to your device settings manually.');
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <MaterialIcons name="multitrack-audio" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={styles.title}>{ENABLE_MICROPHONE_TITLE}</Text>
          <Text style={styles.message}>
            {ENABLE_MICROPHONE}
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={openSettings}>
              <Text style={styles.confirmButtonText}>Settings</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};



const FullScreenLoader = ({ visible, onCancel, onConfirm, value }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <Ionicons name="checkmark-circle" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={styles.title}>Submit Recording?</Text>
          <Text style={styles.message}>
            You have {value} unanswered questions. You may complete them before submitting to AI?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
const RecordDeleteModal = ({ visible, onCancel, onConfirm }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <Ionicons name="checkmark-circle" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={styles.title}>Delete Recording?</Text>
          <Text style={styles.message}>
            If you have an ongoing recording, it will be deleted. Do you want to continue?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const RestartRecordModal = ({ visible, onCancel, onConfirm }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <View style={styles.icon}>
              <Image style={{
                height: 50, width: 50
              }} source={require('assets/images/restart-2.png')} />
            </View>
          </View>
          <Text style={styles.title}>Restart Recording?</Text>
          <Text style={styles.message}>
            Your recording will be deleted,
            is that okay?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Restart</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const AudioRecorder = ({
  isRecording,
  recordingURI,
  setIsRecording,
  setRecordingURI,
  assesId,
  navigation,
  showPauseResume,
  setShowPauseResume,
  initialExpanded = false
}) => {
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  // const { requestWakeLock, releaseWakeLock, isSupported: wakeLockSupported, isActive: wakeLockActive } = useWakeLock();
  // console.log("assesId:", assesId)
  // Animation values
  const translateYAnim = useRef(new Animated.Value(initialExpanded ? 0 : screenHeight)).current;
  const backdropOpacity = useRef(new Animated.Value(initialExpanded ? 0.5 : 0)).current;
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Track if we're in the middle of a gesture
  const isDragging = useRef(false);
  const startY = useRef(0);
  const currentY = useRef(0);

  const currentRoute = useSelector(
    (state: RootState) => state.route.currentRoute,
  );

  const [checkedCount, setCheckedCount] = useState(0);

  const recordingRef = useRef(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [recordedSegments, setRecordedSegments] = useState([]);
  const [isFirstTimeRecording, setIsFirstTimeRecording] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [restartLoading, setRestartLoading] = useState(false);
  const [cancelRecording, setCancelRecording] = useState(false);
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const [playbackPosition, setPlaybackPosition] = useState(0);
  // const [showPauseResume, setShowPauseResume] = useState(false);
  const [assesmentId, setAssesmentId] = useState(0);

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success")
  const [msgTitle, setMsgTitle] = useState("")
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showMicrophoneModal, setShowMicrophoneModal] = useState(false);



  const [uploadError, setUploadError] = useState(null);
  const [recordingFileName, setRecordingFileName] = useState(null);
  const [failedRecordingUri, setFailedRecordingUri] = useState(null);

  const [categories, setCategories] = useState([{
    container: { id: '', heading: '', subHeading: '' },
    items: []
  }]);
  let [totalQuestions, setTotalQuestions] = useState(0);
  let [progress, setProgress] = useState(0);
  const recorderEventEmitter = new NativeEventEmitter(RecorderModule);
  useEffect(() => {
    const checkError = async () => {
      const storedRecording = await getSecureItem("recording");
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);
        setUploadError("Error");
        setRecordingURI(recordingData.recording_uri)
      }
    }

    checkError()
  }, [])
  const fetchAssesment = () => {
    fetchFormDetails(assesId)
      .then((result) => {
        if (result.status == "ok") {
          let assesQues = result.data.question;
          let totalCount = 0

          for (let i = 0; i < assesQues.length; i++) {
            for (let j = 0; j < assesQues[i].items.length; j++) {
              if (!assesQues[i].items[j].isContainer && assesQues[i].items[j].isDisplayCheatSheet) {
                assesQues[i].items[j].checked = false;
                totalCount++
              }
            }
          }
          setTotalQuestions(totalCount);
          setCategories(assesQues);
        } else {
          Alert.alert(result.errorMessage);
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    const eventEmitter = new NativeEventEmitter(RecorderModule);
    const subscription = eventEmitter.addListener("RecordingInterruption", (event) => {
      console.log("Native interruption event:", event);
      if (event.status === 'paused') {
        console.log("paused")
        //setInterruptedPause(true);
        //       pauseTimer(); // ⏸️ Pause the timer when interrupted
        // _pauseRecording()
        setIsRecording(false)
        setIsPaused(true);
        //RecorderModule.pauseRecording();

      } else if (event.status === 'resumed') {
        //setInterruptedPause(false);
        // resumeTimer(); // ▶️ Resume the timer after call ends
        console.log("resumed")
        // _resumeRecording()
        //setIsRecording(true)
        //setIsPaused(false);
      }
    });

    return () => subscription.remove();
  }, []);

  useEffect(() => {
    setAssesmentId(assesId)
    fetchAssesment();
  }, [assesId]);

  // useEffect(() => {
  //   if (Platform.OS === 'web' && wakeLockSupported) {
  //     console.log(`Wake Lock Status: ${wakeLockActive ? 'Active' : 'Inactive'}`);
  //   }
  // }, [wakeLockActive, wakeLockSupported]);


  // useEffect(() => {
  //   // Request wake lock when component mounts and is expanded
  //   if (isExpanded && Platform.OS === 'web') {
  //     requestWakeLock();
  //   }

  //   // Release wake lock when component unmounts or collapses
  //   return () => {
  //     if (Platform.OS === 'web') {
  //       releaseWakeLock();
  //     }
  //   };
  // }, [isExpanded]); // Depend on isExpanded so it activates/deactivates with the recorder

  // useEffect(() => {
  //   if (Platform.OS === 'web') {
  //     if (isRecording && !wakeLockActive) {
  //       requestWakeLock();
  //     } else if (!isRecording && !isExpanded && wakeLockActive) {
  //       releaseWakeLock();
  //     }
  //   }
  // }, [isRecording, isExpanded, wakeLockActive]);

  // useEffect(() => {
  //   if (Platform.OS === 'ios') {
  //     if (isRecording) {
  //       activateKeepAwake();
  //     } else {
  //       deactivateKeepAwake();
  //     }
  //   }

  //   return () => {
  //     if (Platform.OS === 'ios') {
  //       deactivateKeepAwake();
  //     }
  //   };
  // }, [isRecording]);

  // Animated transitions for the bottom sheet
  useEffect(() => {
    if (isExpanded) {
      // Animate bottom sheet up from bottom
      Animated.parallel([
        Animated.spring(translateYAnim, {
          toValue: 0,
          useNativeDriver: true,
          friction: 8,
          tension: 65,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();

      // Prevent body scrolling when sheet is open (web only)
      if (Platform.OS === 'web') {
        document.body.style.overflow = 'hidden';
      }
    } else {
      // Animate bottom sheet down
      Animated.parallel([
        Animated.spring(translateYAnim, {
          toValue: screenHeight,
          useNativeDriver: true,
          friction: 8,
          tension: 65,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(() => {
        // After animation completes, we could do cleanup if needed
      });

      // Restore body scrolling when sheet is closed (web only)
      if (Platform.OS === 'web') {
        document.body.style.overflow = '';
      }
    }

    // Cleanup when component unmounts
    return () => {
      if (Platform.OS === 'web') {
        document.body.style.overflow = '';
      }
    };
  }, [isExpanded]);

  useEffect(() => {
    // Register the closeRecording function globally
    GlobalFunctions.closeRecording = closeRecording;

    // Clean up when the component unmounts
    return () => {
      GlobalFunctions.closeRecording = null;
      // Release wake lock on unmount
      // if (Platform.OS === 'web') {
      //   releaseWakeLock();
      // }
    };
  }, []);

  // Format the time as MM:SS
  const formatTime = seconds => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Increment the timer
  useEffect(() => {
    let interval;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);

  // Clean up audio when component unmounts
  useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);

  // const _startRecording = async () => {
  //   try {
  //     console.log('Requesting permissions...');
  //     const { status } = await Audio.requestPermissionsAsync();
  //     if (status !== 'granted') {
  //       setShowMessageModal(true)
  //       setMessage("Goodly AI uses the microphone to capture clinical conversations during patient visits and continue recording in the background, so your notes aren’t lost if the screen locks or you switch apps. You’ll need to enable microphone access in your settings.")
  //       setMsgTitle("Enable Microphone")
  //       setMessageType("error")
  //       // Alert.alert('Permission Denied', 'Enable microphone in settings.');
  //       return false;
  //     }

  //     await Audio.setAudioModeAsync({
  //       allowsRecordingIOS: true,
  //       playsInSilentModeIOS: true,
  //     });

  //     // console.log("isSafari : "+/^((?!chrome|android).)*safari/i.test(navigator.userAgent))
  //     // Safari-specific workaround
  //     //if (Platform.OS === 'web' && /^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
  //     if (Platform.OS === 'web') {
  //       try {
  //         // Safari requires specific MIME types
  //         const recording = new Audio.Recording();

  //         // Modify recording options for Safari compatibility
  //         const recordingOptions = {
  //           ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
  //           web: {
  //             mimeType: 'audio/mp4', // Try audio/mp4 as it's better supported in Safari
  //             bitsPerSecond: 128000,
  //           },
  //         };

  //         await recording.prepareToRecordAsync(recordingOptions);
  //         await recording.startAsync();
  //         recordingRef.current = recording;
  //         setIsRecording(true);
  //         setIsPaused(false);
  //         setRecordingTime(0);
  //         console.log('Recording started with Safari compatibility mode');
  //         setShowPauseResume(true);
  //         return true;
  //       } catch (safariError) {
  //         console.error('Safari recording attempt failed:', safariError);

  //         // Fallback for Safari: notify user of browser limitations
  //         Alert.alert(
  //           'Browser Limitation',
  //           'Audio recording is not fully supported in Safari. Please try Chrome or Firefox for the best experience.',
  //           [{ text: 'OK' }]
  //         );
  //         return false;
  //       }
  //     } else {
  //       // Standard approach for other browsers and mobile
  //       // const recording = new Audio.Recording();
  //       // await recording.prepareToRecordAsync(
  //       //   Audio.RecordingOptionsPresets.HIGH_QUALITY
  //       // );
  //       // await recording.startAsync();
  //       // recordingRef.current = recording;

  //       const permission = await Audio.requestPermissionsAsync();
  //       if (!permission.granted) {
  //         Alert.alert('Permission Required', 'Microphone permission is required for recording');
  //         return;
  //       }
  //       const uri = await RecorderModule.startRecording();
  //       recordingRef.current = uri;

  //       setIsRecording(true);
  //       setIsPaused(false);
  //       setRecordingTime(0);
  //       console.log('Recording started');
  //       setShowPauseResume(true);
  //       return true;

  //       //await startRecording();
  //     }

  //   } catch (error) {
  //     console.error('Error starting recording:', error);
  //     return false;
  //   }
  // };

  const _startRecording = async () => {
    try {
      console.log('Requesting permissions...');
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        setShowMicrophoneModal(true); // Show the new microphone modal
        return false;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      if (Platform.OS === 'web') {
        try {
          const recording = new Audio.Recording();
          const recordingOptions = {
            ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
            web: {
              mimeType: 'audio/mp4',
              bitsPerSecond: 128000,
            },
          };

          await recording.prepareToRecordAsync(recordingOptions);
          await recording.startAsync();
          recordingRef.current = recording;
          setIsRecording(true);
          setIsPaused(false);
          setRecordingTime(0);
          console.log('Recording started');
          setShowPauseResume(true);
          return true;
        } catch (safariError) {
          console.error('Safari recording attempt failed:', safariError);
          Alert.alert(
            'Browser Limitation',
            'Audio recording is not fully supported in Safari. Please try Chrome or Firefox for the best experience.',
            [{ text: 'OK' }]
          );
          return false;
        }
      } else {
        const permission = await Audio.requestPermissionsAsync();
        if (!permission.granted) {
          setShowMicrophoneModal(true); // Show the new microphone modal
          return false;
        }
        const uri = await RecorderModule.startRecording();
        recordingRef.current = uri;

        setIsRecording(true);
        setIsPaused(false);
        setRecordingTime(0);
        console.log('Recording started');
        setShowPauseResume(true);
        return true;
      }

    } catch (error) {
      console.error('Error starting recording:', error);
      return false;
    }
  };

  const onMicrophoneModalCancel = () => {
    setShowMicrophoneModal(false);
  };

  const onMicrophoneModalSettings = () => {
    setShowMicrophoneModal(false);
  };


  const startRecording = async () => {
    try {
      console.log('Starting recording with native module...');

      // Request permissions first
      const permission = await Audio.requestPermissionsAsync();
      if (!permission.granted) {
        Alert.alert('Permission Required', 'Microphone permission is required for recording');
        return;
      }

      // Start recording using native module
      const uri = await RecorderModule.startRecording();

      console.log('🎙️ Recording started at:', uri);

      setIsRecording(true);
      setIsPaused(false);
      setRecordingTime(0);
      setShowPauseResume(true);
      recordingRef.current = { uri };
      //setRecordingTime(0);
      return true
      //setCurrentRecordingUri(uri);

      // // Show alert about background recording for iOS
      // if (Platform.OS === 'ios') {
      //   Alert.alert(
      //     'Background Recording', 
      //     'Recording will continue even when the app is in the background. Make sure to stop recording when finished.',
      //     [{ text: 'OK' }]
      //   );
      // }

      // Start timer
      //setTimer(0);

      // intervalRef.current = setInterval(() => {
      //   setRecordingTime(t => {
      //     if (t >= 3 * 60 * 60) { // 3 hours max
      //       stopRecording();
      //       return t;
      //     }
      //     return t + 1;
      //   });
      // }, 1000);


    } catch (err) {
      console.error('❌ Failed to start recording:', err);
      Alert.alert('Recording Error', `Failed to start recording: ${err}`);
      setIsRecording(false);
      setCurrentRecordingUri(null);
    }
  };

  const _pauseRecording = async () => {
    console.log('Pausing recording...');
    ToastService.show({
      message: "You recording has been paused.",
      type: "info",
      duration: 4000,
      position: "top",
      bottomOffset: 80,
      autoHide: true,
      backgroundColor: theme.colors.buttonColor,
      icon: "checkmark-circle"

    });
    if (!recordingRef.current) return;

    try {
      //
      if (Platform.OS === "ios") {
        await RecorderModule.pauseRecording();
      } else {
        await recordingRef.current.pauseAsync();
      }
      setIsRecording(false);
      setIsPaused(true);
      setCancelRecording(true)
    } catch (err) {
      console.error('Failed to pause recording', err);
    }
  };

  const _resumeRecording = async () => {
    ToastService.show({
      message: "Your recording has resumed.",
      type: "info",
      duration: 4000,
      position: "top",
      bottomOffset: 80,
      autoHide: true,
      backgroundColor: theme.colors.buttonColor,
      icon: "checkmark-circle"

    });
    console.log("recordingRef.current-->", recordingRef.current)
    try {
      if (recordingRef.current) {

        // 
        if (Platform.OS === "ios") {
          await RecorderModule.resumeRecording();
        } else {
          await recordingRef.current.startAsync();
        }
        setIsRecording(true);
        setIsPaused(false);
        ToastService.show({ message: "Recording resumed", type: "info", backgroundColor: theme.colors.buttonColor });

      }
    } catch (err) {
      console.error('Failed to resume recording', err);
    }
  };

  const _stopRecording = async () => {
    console.log('Stopping recording...');
    try {
      if (!recordingRef.current) {
        console.log('No active recording to stop.');
        return true;
      }
      let uri = null;
      if (Platform.OS === "ios") {
        uri = await RecorderModule.stopRecording();
      } else {
        await recordingRef.current.stopAndUnloadAsync();
        uri = recordingRef.current.getURI();
      }
      // await recordingRef.current.stopAndUnloadAsync();
      // const uri = recordingRef.current.getURI();

      if (!uri) {
        console.error('Failed to retrieve recording URI.');
        return false;
      }

      setRecordedSegments(prev => [...prev, uri]);
      const finalRecording =
        recordedSegments.length > 0 ? [...recordedSegments, uri] : uri;

      setRecordingURI(finalRecording);
      console.log('Recording stopped and stored at', finalRecording);

      recordingRef.current = null;
      setRecordedSegments([]);
      setIsRecording(false);
      setIsPaused(false);

      console.log("Recording stopped....", recordingURI);
      console.log("Recording playing : ", isPaused)
      return finalRecording
    } catch (error) {
      console.error('Error stopping recording:', error);
      return false
    }
  };

  const convertBlobToDataURI = async (blobURI) => {
    const blob = await fetch(blobURI).then((res) => res.blob());
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  };

  const firstRecording = async () => {

    // console.log("recordingURI:" + recordingURI)
    let success = await _startRecording();
    if (success) {
      setIsFirstTimeRecording(false);
      setRecordingURI(null);
    }
  };

  // const _playAudio = async () => {
  //   console.log("_play Audio:", recordingURI);

  //   try {
  //     let sourceURI = recordingURI;

  //     if (Platform.OS === 'web') {
  //                   console.log("2");

  //       sourceURI = await convertBlobToDataURI(recordingURI);
  //     }

  //     if (sound) {
  //           console.log("1");
  //       await sound.playFromPositionAsync(playbackPosition);
  //       setIsPlaying(true);
  //       return;
  //     }

  //     const { sound: newSound } = await Audio.Sound.createAsync(
  //       { uri: sourceURI },
  //       { shouldPlay: true },
  //       onPlaybackStatusUpdate
  //     );
  //     console.log("newSound-->",JSON.stringify(newSound))

  //     setSound(newSound);
  //     setIsPlaying(true);

  //     await newSound.playAsync();
  //   } catch (error) {
  //     console.error("Error playing audio:", error);
  //   }
  // };

  const _playAudio = async () => {
    if (!recordingURI) return;

    try {
      let sourceURI = recordingURI;

      if (Platform.OS === 'web') {
        sourceURI = await convertBlobToDataURI(recordingURI);
      }

      // Always unload previous sound
      if (sound) {
        await sound.unloadAsync();
      }

      // ✅ Set audio mode before playback
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        // interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
        shouldDuckAndroid: true,
        //interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
      });

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: sourceURI },
        { shouldPlay: true },
        onPlaybackStatusUpdate
      );

      setSound(newSound);
      setIsPlaying(true);

      // Optional: Wait for status and debug
      const status = await newSound.getStatusAsync();
      console.log("Playback status:", status);

      await newSound.playAsync();
    } catch (error) {
      console.error("Error playing audio:", error);
    }
  };


  // Add a function to handle playback status updates
  const onPlaybackStatusUpdate = (status) => {
    if (status.isLoaded) {
      setPlaybackPosition(status.positionMillis);

      if (status.didJustFinish) {
        // Audio finished playing
        setIsPlaying(false);
        setPlaybackPosition(0);
      }
    }
  };

  // Add function to pause audio playback
  const _pauseAudio = async () => {

    if (sound && isPlaying) {
      console.log("Pausing audio playback...");
      await sound.pauseAsync();
      setIsPlaying(false);
    }
  };

  const onModalCancel = () => {
    setShowModal(false);
    setRestartLoading(false);
    setCancelRecording(true);
    // setShowPauseResume(false);
  };

  const onRestartModalCancel = () => {
    setRestartLoading(false);
  };

  const restartRecording = () => {
    setRestartLoading(true);
    setShowModal(false);
  };

  const onRestartRecording = async () => {
    let success = await _stopRecording()
    // console.log("----On Restart Recording----->");
    if (success) {
      setIsFirstTimeRecording(true);
      setRecordingURI(null);
      setRecordingTime(0);
      setCancelRecording(false);
      setRestartLoading(false);
      setShowPauseResume(false);
      setSound(null);
    }
  };

  // Function to toggle checkbox state
  const toggleCheckbox = (categoryIndex, questionId) => {
    const updatedCategories = [...categories];
    const questionIndex = updatedCategories[categoryIndex].items.findIndex(q => q.questionCode === questionId);

    if (questionIndex !== -1) {
      let isChecked = !updatedCategories[categoryIndex].items[questionIndex].checked;
      updatedCategories[categoryIndex].items[questionIndex].checked = isChecked;

      let count = checkedCount;
      if (isChecked) {
        count++;
      } else {
        count--;
      }

      if (totalQuestions != 0 && count != 0) {
        setProgress((count / totalQuestions) * 100);
      } else {
        setProgress(0);
      }

      setCheckedCount(count);
      setCategories(updatedCategories);
    }
  };

  // const callServer = async () => {
  //   try {
  //     let result = await uploadRecord(recordingURI, assesmentId);
  //     console.log("result recordingURI---->", recordingURI)
  //     if (result.status == "ok") {

  //       // ToastService.show({
  //       // message: "We have successfully completed the form.",
  //       // type: "info",
  //       // duration: 4000,
  //       // position: "bottom",
  //       // bottomOffset: 80,
  //       // autoHide: false,
  //       // backgroundColor: theme.colors.buttonColor,
  //       // onPress: () => {
  //       // navigation.navigate('Assesment', { assesId: assesId });
  //       // },
  //       // customContent: (
  //       // <View style={{ height: 40, width: 40, backgroundColor: "#fff", borderRadius: 25, justifyContent: "center", alignItems: "center" }}>
  //       // <Image
  //       // source={require('assets/images/sparkles.svg')}
  //       // />
  //       // </View>
  //       // ),
  //       // });
  //     } else {
  //       Alert.alert(result.errorMessage);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   } finally {
  //     setRecordingURI(null)
  //   }
  // };
  const callServer = async (recording_uri: string) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      console.log("pppppppppp", recording_uri);

      await setSecureItem("recording", JSON.stringify({
        "assesmentId": assesmentId,
        "recording_uri": recording_uri,
        "email": await getSecureItem("email"),
      }));

      const fileName = recording_uri.split('/').pop() || 'recording.m4a';
      setRecordingFileName(fileName);

      let result = await uploadRecord(recording_uri, assesmentId, (progress) => {
        console.log("Upload progress:", progress + "%");
        setUploadProgress(progress);
      });
      console.log("Upload successful:", result);
      if (result.status == "ok") {
        // Success handling

        await removeSecureItem("recording");
        if (Platform.OS === "ios") {
          await RecorderModule.deleteFile(recording_uri);
        }
        ToastService.show({
          message: "Hold tight, Goodly AI is processing your request.",
          type: "info",
          duration: 4000,
          position: "bottom",
          bottomOffset: 80,
          autoHide: true,
          backgroundColor: theme.colors.buttonColor,
          onPress: () => {
            console.log("Toast clicked");
          },
          customContent: (
            <View style={{ height: 40, width: 40, backgroundColor: "#fff", borderRadius: 25, justifyContent: "center", alignItems: "center" }}>
              <Image
                source={require('assets/images/sparkles.png')}
              />
            </View>
          ),
        });

        dispatch(hideRecorder());

        setTimeout(() => {
          console.log(" --- Navigating to Dashboard ---");
          navigation.navigate('Dashboard');
        }, 100);
      } else {
        console.log("Upload error:");

        setUploadError(result.errorMessage || 'Upload failed');
        setFailedRecordingUri(recording_uri);
        // Alert.alert(result.errorMessage);
      }
    } catch (error) {
      console.log("Upload error:", error);
      setUploadError(error.message || 'Network error occurred');
      setFailedRecordingUri(recording_uri);
      // Alert.alert('Upload Error', 'Failed to upload recording. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setRecordingURI(null);
    }
  };

  const retryUpload = async () => {
    try {
      const storedRecording = await getSecureItem("recording");
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);
        if (recordingData.email == await getSecureItem("email")) {
          setUploadError(null);

          await callServer(recordingData.recording_uri);
        }
      } else {
        dispatch(hideRecorder());
      }
    } catch (error) {
      console.error("Retry failed:", error);
      Alert.alert('Retry Failed', 'Could not retry upload. Please restart recording.');
    }
  };

  const submitToAI = async () => {
    setShowModal(false);
    let fileName = await _stopRecording()
    console.log("----Submit To AI----->", fileName);
    if (fileName) {
      callServer(fileName);
    }
    // ToastService.show({
    //   message: "Hold tight, Goodly AI is processing your request.",
    //   type: "info",
    //   duration: 4000,
    //   position: "bottom",
    //   bottomOffset: 80,
    //   autoHide: true,
    //   backgroundColor: theme.colors.buttonColor,
    //   onPress: () => {
    //     console.log("Toast clicked");
    //   },
    //   customContent: (
    //     <View style={{ height: 40, width: 40, backgroundColor: "#fff", borderRadius: 25, justifyContent: "center", alignItems: "center" }}>
    //       <Image
    //         source={require('assets/images/sparkles.png')}
    //       />
    //     </View>
    //   ),
    // });

    // Hide the recorder first
    // dispatch(hideRecorder());

    // Then navigate to Dashboard
    // setTimeout(() => {
    //   navigation.navigate('Dashboard');
    // }, 100);
    // console.log("is this hit???")
  };

  const stopRecording = () => {
    // _stopRecording();
    if (!isPaused) {
      _pauseRecording()
    }
    setShowModal(true);
    // setShowPauseResume(false);
  };

  const calculateUnanswered = () => {
    return totalQuestions - checkedCount;
  };

  const renderWebContent = () => {
    return (
      <div
        data-testid="visit-scrollable-container"
        style={{
          overflowY: 'auto',
          height: '100%',
          paddingBottom: FOOTER_HEIGHT,
        }}
      >
        {categories.map((category, categoryIndex) => (
          <View key={categoryIndex} style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>{category?.container.subHeading}</Text>

            {category.items.map((question) => {
              return (
                <>

                  {/* {!question.isContainer && */}
                  {!question.isContainer && question.isDisplayCheatSheet &&
                    <TouchableOpacity
                      key={question.questionCode}
                      style={styles.questionRow}
                      onPress={() => toggleCheckbox(categoryIndex, question.questionCode)}
                      activeOpacity={0.7}
                    >
                      <View style={[
                        styles.checkbox,
                        question.checked ? styles.checkboxChecked : styles.checkboxUnchecked
                      ]}>
                        {question.checked && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </View>
                      <Text style={styles.questionText}>{question.description}</Text>
                    </TouchableOpacity>
                  }
                </>
              )
            }
            )}

            {categoryIndex < categories.length - 1 && <View style={styles.divider} />}
          </View>
        ))}
      </div>
    )
  }

  const renderNativeContent = () => {
    return (
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: FOOTER_HEIGHT }}
      >
        {categories.map((category, categoryIndex) => (
          <View key={categoryIndex} style={styles.categoryContainer}>
            <Text style={styles.categoryTitle}>{category?.container.subHeading}</Text>

            {category.items.map((question) => {
              return (
                <>
                  {!question.isContainer && question.isDisplayCheatSheet &&
                    <TouchableOpacity
                      key={question.questionCode}
                      style={styles.questionRow}
                      onPress={() => toggleCheckbox(categoryIndex, question.questionCode)}
                      activeOpacity={0.7}
                    >
                      <View style={[
                        styles.checkbox,
                        question.checked ? styles.checkboxChecked : styles.checkboxUnchecked
                      ]}>
                        {question.checked && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </View>
                      <Text style={styles.questionText}>{question.description}</Text>
                    </TouchableOpacity>
                  }
                </>
              )
            }
            )}

            {categoryIndex < categories.length - 1 && <View style={styles.divider} />}
          </View>
        ))}
      </ScrollView>
    )
  }
  const UploadProgressBar = ({ progress, error, fileName, recordingUri, onRetry }) => {
    if (error) {
      return (
        <View style={styles.uploadContainer}>
          {/* <View style={styles.errorContainer}>
          <Ionicons name="warning" size={24} color="#FF6B6B" />
          <Text style={styles.errorTitle}>Upload Failed</Text>
        </View> */}
          {/* {fileName && ( */}
          <Text style={styles.fileName}>{UPLOAD_MINI_ERROR}</Text>
          {/* )} */}
          {/* {recordingUri && (
          <Text style={styles.recordingUri}>URI: {recordingUri}</Text>
        )} */}
          {/* <Text style={styles.errorMessage}>{error}</Text> */}
          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Text style={styles.retryButtonText}>Retry Submission</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.uploadContainer}>
        <View style={styles.uploadHeader}>
          <Text style={styles.uploadTitle}>Upload in Progress</Text>
          <Text style={[styles.uploadPercentage, { color: theme.colors.buttonColor }]}>
            {progress}%
          </Text>
        </View>

        <View style={styles.progressBarContainerUpload}>
          <Animated.View
            style={[
              styles.progressBarUpload,
              {
                width: `${progress}%`,
                backgroundColor: theme.colors.buttonColor,
                // Add smooth animation on React Native
                ...(Platform.OS !== 'web' && {
                  transform: [{ scaleX: progress / 100 }],
                  transformOrigin: 'left'
                })
              }
            ]}
          />
        </View>
        {/* <View style={styles.progressBarContainerUpload}>
              <View style={[styles.progressBarUpload, { width: `${progress}%` }]} />
            </View> */}
        <Text style={styles.uploadSubtext}>
          {progress < 100
            ? "Please wait while your recording is being processed..."
            : "Finalizing upload..."
          }
        </Text>
      </View>
    );
  };
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        if (isExpanded) {
          // If the recorder is expanded, minimize it
          // toggleExpand();
          if (showPauseResume || recordingURI != null) {
            // Recording in progress or completed
            console.log("Recording is active or completed");
            toggleExpand();
          } else {
            // No active recording
            console.log("No active recording");
            dispatch(hideRecorder());
          }
          return true; // Prevent default back behavior
        }
        // Otherwise let the default back action occur
        return false;
      };

      // Add event listener for hardware back button (Android)
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      // For web browser back button
      if (Platform.OS === 'web') {
        const handlePopState = () => {
          if (isExpanded) {
            if (showPauseResume || recordingURI != null) {
              // Recording in progress or completed
              console.log("Recording is active or completed");
              toggleExpand();
            } else {
              // No active recording
              console.log("No active recording");
              dispatch(hideRecorder());
            }
          }
        };

        window.addEventListener('popstate', handlePopState);

        return () => {
          window.removeEventListener('popstate', handlePopState);
        };
      }

      // Clean up the event listener
      const backHandler = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => backHandler.remove();
    }, [isExpanded, showPauseResume, recordingURI])
  );

  // Touch handlers for the bottom sheet
  const handleTouchStart = (e) => {
    if (Platform.OS === 'web') {
      startY.current = e.nativeEvent.pageY;
      isDragging.current = true;
    }
  };

  const handleTouchMove = (e) => {
    if (Platform.OS === 'web' && isDragging.current) {
      currentY.current = e.nativeEvent.pageY;
      const deltaY = currentY.current - startY.current;

      // Only allow dragging down, not up (when already at top)
      if (deltaY > 0) {
        translateYAnim.setValue(deltaY);

        // Gradually reduce backdrop opacity as we drag down
        const newOpacity = Math.max(0, 0.5 - (deltaY / (screenHeight * 2)));
        backdropOpacity.setValue(newOpacity);
      }
    }
  };

  const handleTouchEnd = () => {
    if (Platform.OS === 'web' && isDragging.current) {
      isDragging.current = false;

      // If dragged more than 30% of screen height, close the sheet
      if (currentY.current - startY.current > screenHeight * 0.3) {
        //console.log("going to toggle ")
        // toggleExpand();
        goBack()
      } else {
        // Otherwise snap back to open position
        Animated.parallel([
          Animated.spring(translateYAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 65,
          }),
          Animated.timing(backdropOpacity, {
            toValue: 0.5,
            duration: 300,
            useNativeDriver: true,
          })
        ]).start();
      }
    }
  };
  const closeRecording = async () => {
    if (uploadError) {


      const storedRecording = await getSecureItem("recording");
      if (storedRecording) {
        const recordingData = JSON.parse(storedRecording);
        if (recordingData.email == await getSecureItem("email")) {

          await removeSecureItem("recording");
          await RecorderModule.deleteFile(recordingData.recording_uri);
        }
        dispatch(hideRecorder());
      }
      } else {
        let success = await _stopRecording()
        console.log("----Close Recording----->");
        if (success) {
          setRecordingURI(null)
          dispatch(hideRecorder());
        }
      }
    }
    const toggleExpand = () => {
      // console.log("Expnded : "+isExpanded)
      setIsExpanded(!isExpanded);
      dispatch(toggleExpanded());
    };

    const goBack = () => {
      console.log("Clicking on go back")
      if (showPauseResume || recordingURI != null || uploadError) {
        toggleExpand()
      } else {
        dispatch(hideRecorder())
      }
    }
    const onMessageModalCancel = () => {
      setShowMessageModal(false);
    };
    const onDeleteModalCancel = () => {
      setShowDeleteModal(false);
    }
    const openDeleteModal = () => {
      console.log("Open Delete Modal:", showPauseResume, recordingURI)
      if (showPauseResume || recordingURI != null || uploadError) {
        setShowDeleteModal(true);
      } else {
        dispatch(hideRecorder())
      }
    }
    if (!isExpanded) {
      return (
        <LinearGradient colors={['#4F47E5', '#6366F1']} style={styles.miniPlayerGradient}>
          <View style={miniStyles.miniPlayer}>
            <DynamicMessageModalWithConfirm visible={showMessageModal} onCancel={onMessageModalCancel} message={message} title={msgTitle} iconComponent={<MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" />} />
            {(uploadError || isUploading) ? (
              <>
                {uploadError ? (
                  <>
                    <TouchableOpacity onPress={retryUpload}>
                      <FontAwesome name="refresh" size={20} color="#fff" />
                    </TouchableOpacity>
                    <View style={{ flex: 1 }}>
                      <Text style={miniStyles.miniMsg}>{UPLOAD_MINI_ERROR}</Text>
                    </View>
                  </>
                ) : (
                  <View style={{ flex: 1 }}>
                    <Text style={miniStyles.miniMsg}>Uploading recording {uploadProgress}%</Text>
                  </View>
                )}
              </>
            ) : (
              <>
                <TouchableOpacity
                  onPress={
                    isRecording
                      ? _pauseRecording
                      : isPaused
                        ? _resumeRecording
                        : firstRecording
                  }
                >
                  {isRecording ? (
                    <FontAwesome name="pause" size={20} color="#fff" />
                  ) : (
                    <FontAwesome name="play" size={20} color="#fff" />
                  )}
                </TouchableOpacity>


                <Text style={miniStyles.miniTimer}>{formatTime(recordingTime)}</Text>
              </>
            )}
            <TouchableOpacity onPress={toggleExpand}>

              <Feather name="chevron-up" size={28} color="#fff" />

            </TouchableOpacity>
          </View>
        </LinearGradient>
      );
    }

    // For the expanded view (bottom sheet)


    // if (!isExpanded) {
    //   return (
    //     <>
    //       <DynamicMessageModalWithConfirm 
    //         visible={showMessageModal} 
    //         onCancel={onMessageModalCancel} 
    //         message={message} 
    //         title={msgTitle} 
    //         iconComponent={<MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" />} 
    //       />

    //       {/* Show upload error/progress or normal mini player */}
    //       {/* {(isUploading || uploadError) ? (
    //         <View style={styles.miniErrorContainer}>
    //           <UploadProgressBar
    //             progress={uploadProgress}
    //             error={uploadError}
    //             fileName={recordingFileName}
    //             recordingUri={failedRecordingUri}
    //             onRetry={retryUpload}
    //           />
    //           <TouchableOpacity style={styles.miniExpandButton} onPress={toggleExpand}>
    //             <Feather name="chevron-up" size={20} color="#4F47E5" />
    //           </TouchableOpacity>
    //         </View>
    //       ) : ( */}
    //         <LinearGradient colors={['#4F47E5', '#6366F1']} style={styles.miniPlayerGradient}>
    //           <View style={miniStyles.miniPlayer}>
    //             <TouchableOpacity
    //               onPress={
    //                 isRecording
    //                   ? _pauseRecording
    //                   : isPaused
    //                     ? _resumeRecording
    //                     : firstRecording
    //               }
    //             >
    //               {isRecording ? (
    //                 <FontAwesome name="pause" size={20} color="#fff" />
    //               ) : (
    //                 <FontAwesome name="play" size={20} color="#fff" />
    //               )}
    //             </TouchableOpacity>

    //             <Text style={miniStyles.miniTimer}>{formatTime(recordingTime)}</Text>

    //             <TouchableOpacity onPress={toggleExpand}>
    //               <Feather name="chevron-up" size={28} color="#fff" />
    //             </TouchableOpacity>
    //           </View>
    //         </LinearGradient>
    //       {/* )} */}
    //     </>
    //   );
    // }

    return (
      <>
        {/* Backdrop overlay */}
        <Animated.View
          style={[
            styles.backdrop,
            { opacity: backdropOpacity }
          ]}
          onTouchStart={() => toggleExpand()}
        />

        {/* Bottom Sheet */}
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [{ translateY: translateYAnim }],
            }
          ]}
        // onTouchStart={handleTouchStart}
        // onTouchMove={handleTouchMove}
        // onTouchEnd={handleTouchEnd}
        >
          <FullScreenLoader visible={showModal} onCancel={onModalCancel} onConfirm={submitToAI} value={calculateUnanswered()} />
          <RestartRecordModal visible={restartLoading} onCancel={onRestartModalCancel} onConfirm={onRestartRecording} />
          <RecordDeleteModal visible={showDeleteModal} onCancel={onDeleteModalCancel} onConfirm={closeRecording} />
          <DynamicMessageModalWithConfirm visible={showMessageModal} onCancel={onMessageModalCancel} message={message} title={msgTitle} iconComponent={<MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" />} />

          <MicrophonePermissionModal
            visible={showMicrophoneModal}
            onCancel={onMicrophoneModalCancel}
            onSettings={onMicrophoneModalSettings}
          />

          {/* Pull handle */}
          <TouchableOpacity style={styles.handleContainer} onPress={goBack}>
            <View style={styles.handle} />
          </TouchableOpacity>

          {/* Header */}
          <View style={styles.sheetHeader}>
            <TouchableOpacity style={styles.backButton} onPress={goBack}>
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
            <Text allowFontScaling={false} style={styles.headerTitle}>Recording in Progress</Text>
            <TouchableOpacity onPress={openDeleteModal} style={styles.closeButton}>
              {/* <X size={24} color="#000" /> */}
              <Ionicons name="close" size={24} color="#000" />
            </TouchableOpacity>
          </View>
          {/* <View style={styles.sheetHeader}>
 
 <Text style={styles.headerTitle}>Recording In Progress</Text>
 <TouchableOpacity onPress={toggleExpand} style={styles.closeButton}>
 <X size={24} color="#000" />
 </TouchableOpacity>
 </View> */}

          {/* Progress Section */}
          {!loading && categories.length > 0 && (
            <View style={styles.progressSection}>
              <View style={styles.progressContainer}>
                <Text style={styles.progressText}>Questions</Text>
                <Text style={styles.progressCount}>{checkedCount}/{totalQuestions}</Text>
              </View>
              <View style={styles.progressBarContainer}>
                <View style={[styles.progressBar, { width: `${progress}%` }]} />
              </View>
            </View>
          )}

          {/* Content */}
          <View style={styles.sheetContent}>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#4F47E5" />
                <Text style={styles.loadingText}>Loading questions...</Text>
              </View>
            ) : categories.length > 0 ? (
              Platform.OS === 'web' ? renderWebContent() : renderNativeContent()
            ) : (
              <View style={styles.blankTextContainer}>
                <Text style={styles.blankText}>No Question available</Text>
              </View>
            )}
          </View>

          {/* Footer */}
          <View style={styles.sheetFooter}>
            {(isUploading || uploadError) ? (
              <UploadProgressBar
                progress={uploadProgress}
                error={uploadError}
                fileName={recordingFileName}
                recordingUri={failedRecordingUri}
                onRetry={retryUpload}
              />
            ) : (
              <View style={styles.timerContainer}>
                <Text style={styles.timerText}>{formatTime(recordingTime)}</Text>

                {/* Control buttons */}
                <View style={styles.controlsContainer}>
                  {isFirstTimeRecording ? (
                    <TouchableOpacity style={styles.startButton} onPress={firstRecording}>
                      <View style={styles.startButtonInner}>
                        <FontAwesome name="microphone" size={20} color="#ffffff" />
                        <Text style={styles.startText}>Start Recording</Text>
                      </View>
                    </TouchableOpacity>
                  ) : (
                    <>
                      {/* Pause/Resume Button */}
                      {showPauseResume &&
                        <TouchableOpacity style={styles.controlButton} onPress={
                          isRecording
                            ? _pauseRecording
                            : isPaused
                              ? _resumeRecording
                              : _startRecording
                        }>
                          <View style={styles.pauseButton}>
                            {isRecording &&
                              <FontAwesome name="pause" size={18} color="#ffffff" />
                            }
                            {isPaused &&
                              <FontAwesome name="play" size={18} color="#ffffff" />
                            }
                          </View>
                        </TouchableOpacity>
                      }
                      {/* Restart Button */}
                      {cancelRecording &&
                        <TouchableOpacity style={styles.controlButton} onPress={restartRecording}>
                          <Feather name="refresh-ccw" size={28} color="#ffffff" />
                        </TouchableOpacity>
                      }
                      {/* Stop/Submit Button */}
                      <TouchableOpacity style={styles.controlButton} onPress={stopRecording}>
                        <FontAwesome name="check" size={18} color="#ffffff" />
                      </TouchableOpacity>
                    </>
                  )}
                </View>
              </View>
            )}
          </View>

        </Animated.View>
      </>
    );
  };

  const miniStyles = StyleSheet.create({
    miniPlayer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
      paddingHorizontal: 20,
    },
    miniTimer: {
      color: '#fff',
      fontSize: 20,
      fontFamily: 'Poppins_600SemiBold',
    },
    miniMsg: {
      color: '#fff',
      fontSize: 16,
      fontFamily: 'Poppins_600SemiBold',
      marginLeft: 10
    },
    flipIcon: {
      transform: [{ rotateX: '180deg' }],
    },
  });

  const styles = StyleSheet.create({
    // Mini player styles
    miniPlayerGradient: {
      height: MINI_PLAYER_HEIGHT,
      width: '100%',
      borderRadius: 8,
      // borderTopLeftRadius: 16,
      // borderTopRightRadius: 16,
      justifyContent: 'center',
    },
    errorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
    },
    errorTitle: {
      fontSize: 18,
      fontFamily: 'Poppins_600SemiBold',
      color: '#FF6B6B',
      marginLeft: 8,
    },
    fileName: {
      fontSize: 16,
      fontFamily: 'Poppins_400Regular',
      color: '#FF6B6B',
      marginBottom: 5,
      textAlign: 'center',
    },
    recordingUri: {
      fontSize: 12,
      fontFamily: 'Poppins_400Regular',
      color: '#888',
      marginBottom: 8,
      textAlign: 'center',
      paddingHorizontal: 10,
    },
    errorMessage: {
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
      color: '#FF6B6B',
      textAlign: 'center',
      marginBottom: 15,
    },
    retryButton: {
      backgroundColor: theme.colors.buttonColor,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 24,
      alignItems: 'center',
    },
    retryButtonText: {
      color: '#FFF',
      fontSize: 16,
      fontFamily: 'Poppins_500Medium',
    },

    backButton: {
      marginRight: 8,
    },
    // Bottom sheet styles
    backdrop: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: '#000',
      zIndex: 998,
    },
    bottomSheet: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: '#f6f6f6ff',
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      height: screenHeight - 100, // Slightly smaller than full screen to show it's a sheet
      zIndex: 999,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -3 },
      shadowOpacity: 0.1,
      shadowRadius: 10,
      elevation: 20,
      overflow: 'hidden',
    },
    handleContainer: {
      alignItems: 'center',
      paddingVertical: 10,
    },
    handle: {
      width: 40,
      height: HANDLE_HEIGHT,
      borderRadius: 2.5,
      backgroundColor: '#E0E0E0',
    },
    sheetHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingBottom: 15,
      // borderBottomWidth: 1,
      // borderBottomColor: '#EAEAEA',
    },
    headerTitle: {
      fontSize: 20,
      fontFamily: 'Poppins_500Medium',
      color: '#000',
    },
    closeButton: {
      padding: 5,
    },
    sheetContent: {
      flex: 1,
    },
    sheetFooter: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: '#FFFFFF',
      // borderTopWidth: 1,
      // borderTopColor: '#EAEAEA',
      // paddingVertical: 15,
      paddingBottom: 15,
      paddingHorizontal: 20,
      minHeight: FOOTER_HEIGHT,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      // Shadow for Android
      elevation: 5,
    },

    // Progress section
    progressSection: {
      paddingVertical: 10,
      // backgroundColor: '#FFFFFF',
      paddingHorizontal: 20,
    },
    progressContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    progressText: {
      fontSize: 14,
      color: '#000',
      fontFamily: 'Poppins_400Regular',
    },
    progressCount: {
      fontSize: 14,
      color: '#000',
      fontFamily: 'Poppins_400Regular',
    },
    progressBarContainer: {
      height: 6,
      backgroundColor: '#EAEAEA',
      borderRadius: 10,
    },
    progressBar: {
      height: 6,
      backgroundColor: '#0079FE',
      borderRadius: 10,
    },
    progressBarContainerUpload: {
      height: 8,
      backgroundColor: '#EAEAEA', // Light green background
      borderRadius: 4,
      width: '100%', // Full width
      marginBottom: 15,
    },
    progressBarUpload: {
      height: 6,
      backgroundColor: '#0079FE',
      borderRadius: 10,
    },

    // Content
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    loadingText: {
      marginLeft: 10,
      fontSize: 16,
      fontFamily: 'Poppins_500Medium',
      color: '#4F47E5',
    },
    scrollView: {
      flex: 1,
    },
    categoryContainer: {
      paddingHorizontal: 20,
    },
    categoryTitle: {
      fontSize: 16,
      fontFamily: 'Poppins_600SemiBold',
      marginBottom: 12,
      marginTop: 18,
      color: '#000',
    },
    questionRow: {
      flexDirection: 'row',
      marginBottom: 16,
      alignItems: "center"
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 11,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    checkboxChecked: {
      backgroundColor: '#007AFF',
    },
    checkboxUnchecked: {
      borderWidth: 1,
      borderColor: '#999',
    },
    questionText: {
      fontSize: 14,
      flex: 1,
      fontFamily: 'Poppins_500Medium',
      color: '#000',
    },
    divider: {
      height: 1,
      backgroundColor: '#E0E0E0',
    },
    blankTextContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    blankText: {
      textAlign: 'center',
      fontSize: 20,
      fontFamily: 'Poppins_500Medium',
    },

    // Footer
    timerContainer: {
      alignItems: 'center',
    },
    timerText: {
      fontSize: 30,
      marginBottom: 10,
      fontFamily: 'Poppins_500Medium',
    },
    controlsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
    },
    controlButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: theme.colors.buttonColor,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 8,
    },
    pauseButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: 30,
      height: 30,
    },
    startButton: {
      // width: 198,
      // height: 46,
      backgroundColor: theme.colors.buttonColor,
      borderRadius: 40,
      color: '#FFFFFF',
    },
    startButtonInner: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 15,
      paddingVertical: 14,
      paddingHorizontal: 20
    },
    startText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontFamily: 'Poppins_500Medium',
    },

    // Modal styles
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      width: width * 0.95,
      backgroundColor: '#FFF',
      paddingVertical: 24,
      paddingHorizontal: 20,
      borderRadius: 16,
      alignItems: 'center',
      elevation: 5,
    },
    iconContainer: {
      marginBottom: 25,
    },
    title: {
      color: '#000',
      textAlign: 'center',
      fontFamily: 'Poppins_600SemiBold',
      fontSize: 24,
      fontStyle: 'normal',
    }, icon: {
      width: 60,
      height: 60,
      backgroundColor: '#EFF6FF',
      borderRadius: 30,
      alignItems: 'center',
      justifyContent: 'center',
      display: 'flex',
    },
    message: {
      color: '#7C7887',
      textAlign: 'center',
      fontFamily: 'Poppins_400Regular',
      fontSize: 18,
      fontStyle: 'normal',
      fontWeight: '400',
      marginTop: 10,
      marginBottom: 15
    },
    buttonContainer: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      marginTop: 15,
    },
    cancelButton: {
      flex: 1,
      borderWidth: 1,
      borderColor: theme.colors.buttonColor,
      paddingVertical: 10,
      borderRadius: 24,
      marginRight: 8,
      alignItems: 'center',
    },
    cancelButtonText: {
      color: theme.colors.buttonColor,
      fontSize: 16,
      fontFamily: 'Poppins_500Medium',
    },
    confirmButton: {
      flex: 1,
      backgroundColor: theme.colors.buttonColor,
      paddingVertical: 10,
      borderRadius: 24,
      marginLeft: 8,
      alignItems: 'center',
    },
    confirmButtonText: {
      color: '#FFF',
      fontSize: 16,
      fontFamily: 'Poppins_500Medium',
    },

    // progressbar 
    uploadContainer: {
      alignItems: 'center',
      paddingTop: 10,
      // paddingVertical: 20,
    },
    uploadHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
      marginBottom: 15,
    },
    uploadTitle: {
      fontSize: 18,
      fontFamily: 'Poppins_600SemiBold',
      color: '#000',
    },
    uploadPercentage: {
      fontSize: 18,
      fontFamily: 'Poppins_600SemiBold',
      color: theme.colors.buttonColor,
    },
    uploadProgressBar: {
      height: 8,
      backgroundColor: theme.colors.buttonColor,
      borderRadius: 4,
      transition: 'width 0.3s ease', // Smooth animation on web
    },
    uploadSubtext: {
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
      color: '#7C7887',
      textAlign: 'center',
      marginTop: 10,
    },
    disabledButton: {
      opacity: 0.5,
    },
    // miniErrorContainer: {
    //   backgroundColor: '#fff',
    //   borderRadius: 8,
    //   borderWidth: 1,
    //   borderColor: '#FF6B6B',
    //   paddingHorizontal: 15,
    //   paddingVertical: 10,
    //   flexDirection: 'row',
    //   alignItems: 'center',
    //   justifyContent: 'space-between',
    // },
    // miniExpandButton: {
    //   width: 32,
    //   height: 32,
    //   borderRadius: 16,
    //   backgroundColor: '#F0F0F0',
    //   alignItems: 'center',
    //   justifyContent: 'center',
    //   marginLeft: 10,
    // },
  });

  export default AudioRecorder;