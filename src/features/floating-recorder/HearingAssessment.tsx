import React, { useState } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  StatusBar 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { AllQuestion } from './components/AllQuestion';
import theme from '@/src/theme';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
// import { ChevronLeft } from 'lucide-react-native';
import { screenHeight } from '@/src/utils/ScreenUtils';
import { Feather } from '@expo/vector-icons';

const Tab = createMaterialTopTabNavigator();

// Define the hearing assessment section
export const HearingAssessment = ({ route }) => {
  const navigation = useNavigation();
  const onBackClick = () => {
    navigation.goBack();
  };
  
  const assesId = route.params?.assesId;

  // Create a custom screen component that passes the visitId to AllQuestion
  const renderBackButton = () => {
    return (
      <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
        {/* <ChevronLeft color={theme.colors.text} size={24} /> */}
       <Feather name="chevron-left" size={24} color={theme.colors.text} />
      </TouchableOpacity>
    );
  };
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {renderBackButton()}
        <View >
          <Text style={styles.headerText}>Review Assessment</Text>
        </View>
      </View>
      <View style={styles.tabContainer}>
        <Tab.Navigator 
          screenOptions={({ route }) => ({
            tabBarStyle: styles.tabBar,
            tabBarLabelStyle: styles.tabLabel,
            tabBarLabel: ({ focused }) =>
              focused ? (
                <Text style={[styles.tabLabel, styles.tabLabelSelected]}>
                  {route.name}
                </Text>
              ) : (
                <Text style={styles.tabLabel}>{route.name}</Text>
              ),
            tabBarIndicatorStyle: styles.tabIndicator,
            lazy: true,
          })}
        >
          <Tab.Screen name="All Questions" initialParams={{"visitId":assesId}}  component={AllQuestion} />
          
        </Tab.Navigator>
      </View>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    // fontFamily: "Poppins_600SemiBold",
    // fontSize: 20,
    // color: theme.colors.text,
    // paddingHorizontal: 12,
    // textAlign: "center",
    // marginBottom: 16,
    // position:"relative"
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    // borderBottomWidth: 1,
    // borderBottomColor: "#E2E8F0",
    backgroundColor: "#FFFFFF",
    position: "relative",
    height: 40,
    paddingTop:10
  },
  headerText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 20,
    color: '#000',
    paddingHorizontal: 10,
    textAlign: "center",
    
  },
  backButton: {
    alignItems: "center",
    justifyContent: "center",
    marginRight: 14,
    marginLeft:5
    // gap: 8,
    // position: "absolute",
    // top: 10,
    // left: 16,
    // zIndex: 10,
    // width: 40,
    // height: 40,
    // backgroundColor: "#FFFFFF",
    // borderRadius: 20,
    // justifyContent: "center",
    // alignItems: "center",
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 8,
    // elevation: 3,
  },
  
  
  tabBar: {
    backgroundColor: "#fff",
  },
  tabLabel: {
    color: '#7C7887',
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
  },
  tabLabelSelected: {
    color: '#1D73D6',
  },
  tabIndicator: {
    backgroundColor: '#1D73D6',
    height: 3,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  sectionContainer: {
    padding: 15,
  },
  
  detailItem: {
    fontSize: 16,
    marginBottom: 5,
  },
  tabContainer: {
    display: "flex",
    flex: 1,
    height: screenHeight - 40,
  },
});

export default HearingAssessment;