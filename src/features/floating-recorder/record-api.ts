import { Platform } from "react-native";
import {uploadApiClient,apiClient} from "src/api/apiClient";



// export const uploadRecord = (audioUri,assessmentId) => {
//   console.log("audio uri:",audioUri)
//   console.log("assessmentId:",assessmentId)

//     let formData = new FormData();
      
//       // Append the audio file
//       formData.append('audio', {
//         uri: audioUri,
//         name: `recording-${Date.now()}.m4a`,
//         type: 'audio/x-m4a',
//       });
//       formData.append("assessmentId",assessmentId)

//         return uploadApiClient
//           .post(
//             "clinician/upload-audio",
//             // "testUploadFile",
//             formData,
//           )
//           .then((response) => response.data)
//           .catch((error) => {
//             console.log(error)
//             throw error.response?.data?.message || "Record Upload failed";
//           });
// }
export const  uploadRecord = async (audioUri,assessmentId,onProgress) => {
  let formData = new FormData();
  console.log("Sending for assesmentId:", assessmentId);
  if (Platform.OS === 'web') {
    // Convert blob URI to Blob object
    const response = await fetch(audioUri);
    const blob = await response.blob();

    // Create a File object from the blob
    const file = new File([blob], `recording-${Date.now()}.m4a`, {
      type: 'audio/x-m4a', // 'audio/mp4' works better for m4a files on web
    });

    formData.append('audio', file);
  } else {
    formData.append('audio', {
      uri: audioUri,
      name: `recording-${Date.now()}.m4a`,
      type: 'audio/x-m4a',
    });
  }

  formData.append('assessmentId', assessmentId);

  try {
    // const response = await uploadApiClient.post('v1/clinician/upload-audio', formData);
    const response = await uploadApiClient.post('v1/clinician/upload-audio', formData, {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.lengthComputable && onProgress) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      },
      // Add timeout for better UX
      timeout: 300000, // 5 minutes timeout
    });
    // console.log("Response ----------"+response.data);
    return response.data
    // 
  } catch (error) {
    throw error.response?.data?.message || 'Record Upload failed';
  }
};
export const fetchAssesmentId=(visitId)=>{
    return apiClient
      .get(`v1/visit/assessment?visitId=${visitId}`)
      .then((response) => response.data)
      .catch((error) => {
        throw error.response?.data?.message || "Failed to Get AssesmentId";
      });
}
export const fetchFormDetails=(assessmentId:string)=>{
    return apiClient
      .get(`v1/visit/assessment/${assessmentId}`)
      .then((response) => response.data)
      .catch((error) => {
        throw error.response?.data?.message || "Failed to load Form Details";
      });
}
export const updateAssesment=(data:any,assesmentId:number)=>{
  return apiClient
  .put(`v1/visit/assessment/${assesmentId}`, data)
  .then((response) => response.data)
  .catch((error) => {
    console.log(error)
    throw error.response?.data?.message || "Update Assesment failed";
  });
}

