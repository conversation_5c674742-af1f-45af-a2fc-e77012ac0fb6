import { getSecureItem } from "@/src/utils/cryptoHelper";
import { apiClient } from "src/api/apiClient";

export const saveUserData = (data: any) => {
    return apiClient
        .put(`v1/auth/me`, data)
        .then((response) => response.data)
        .catch((error) => {
            console.log("Error:", error);
            throw error.response?.data?.message || "Failed to Save Form ";
        });
};
export const fetchDiscipline = async() => {
   //let names = window.location.hostname.split(".")
         let names =await getSecureItem("org");
    
    return apiClient
        .get(`v1/visit/discipline?limit=20&page=1&x-tenant-id=${names}`)
        .then((response) => response.data)
        .catch((error) => {
            console.log("Error:", error);
            throw error.response?.data?.message || "Failed to Fetch Discipline";
        });
};
export const logOut = async (refreshToken: string) => {
    try {
      const response = await apiClient.post("v1/auth/logout", { refreshToken });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || "Logout failed";
    }
  };
  export const deleteAccount = async ({email}:{email:string}) => {
    try {
      const response = await apiClient.post("/v1/auth/deleteAccount", { email });
      return response.data;
    } catch (error: any) {
      throw error.response?.data?.message || "Deletion failed";
    }
  };