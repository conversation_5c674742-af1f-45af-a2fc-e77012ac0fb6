import React, { cache, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Platform,
  TextInput as NativeTextInput,
  Modal,
  Dimensions
} from 'react-native';
import { TextInput } from 'react-native-paper';
import { useNavigation } from "@react-navigation/native";
// import { ChevronLeft, LayoutDashboard } from "lucide-react-native";
import theme from "src/theme";
import { Feather, Ionicons } from '@expo/vector-icons';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { globalStyles } from '@/src/styles';
import { saveUserData, fetchDiscipline } from '../profile-api';
import FullScreenLoader from '@/src/utils/MobileLoader';

import { validateEmail, validateMobile, formatPhoneNumber } from 'src/utils/ProfileUtils';
import { MessageModal } from '@/src/utils/MessageModal';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import DateTimePicker from '@react-native-community/datetimepicker';
import { getSecureItem, setSecureItem } from '@/src/utils/cryptoHelper';

const imageMap: { [key: string]: any } = {
  profile: require('assets/images/profile.svg'),
  email: require('assets/images/email.svg'),
  mobile: require('assets/images/mobile.svg'),
  badge: require('assets/images/badge.svg'),
  date: require('assets/images/date.svg'),
};

const Dropdown = ({
  label = "",
  options = [],
  initialValue = "",
  onChange,
  placeholder = "Select an option",
  valueKey = "value",
  labelKey = "label",
  image = "chevron-down-outline",
  imageName = "",
  disabled = false
}) => {
  const [value, setValue] = useState(initialValue);
  const [isOpen, setIsOpen] = useState(false);
  const [focused, setFocused] = useState(false);
  const dropdownRef = useRef(null);

  // Update state when initialValue prop changes
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleOptionSelect = (option) => {
    setValue(option);
    setIsOpen(false);
    if (onChange) {
      onChange(option.value);
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    setFocused(!isOpen);
  };

  // Find selected option's label
  const getDisplayValue = () => {
    if (!value) return '';
    if (typeof value === 'string' || typeof value === 'number') {
      const found = options.find((opt) => {
        return opt[valueKey] === value
      }
      );
      return found ? found[labelKey] : "";
    }
    return value[labelKey] || '';
  };

  return (
    <View style={dropdownStyles.container} ref={dropdownRef}>
      {disabled ?
        <View
          style={[dropdownStyles.field, styles.disabled]}
        >
          <View style={dropdownStyles.fieldIcon}>
            <Image source={imageMap[imageName]} />
            {/* <Ionicons name={iconname} size={18} color="#999" /> */}
          </View>
          <View style={dropdownStyles.fieldContent}>
            <Text style={dropdownStyles.fieldLabel}>{label}</Text>
            <View style={dropdownStyles.inputContainer}>
              {getDisplayValue() ? (
                <Text style={dropdownStyles.fieldInput}>{getDisplayValue()}</Text>
              ) : (
                <Text style={dropdownStyles.placeholder}>{placeholder}</Text>
              )}
            </View>
          </View>
        </View>
        :
        <TouchableOpacity
          style={[dropdownStyles.field, focused && dropdownStyles.fieldFocused]}
          activeOpacity={0.9}
          onPress={toggleDropdown}
        >
          <View style={dropdownStyles.fieldIcon}>
            <Image source={imageMap[imageName]} />
            {/* <Ionicons name={iconname} size={18} color="#999" /> */}
          </View>
          <View style={dropdownStyles.fieldContent}>
            <Text style={dropdownStyles.fieldLabel}>{label}</Text>
            <View style={dropdownStyles.inputContainer}>
              {getDisplayValue() ? (
                <Text style={dropdownStyles.fieldInput}>{getDisplayValue()}</Text>
              ) : (
                <Text style={dropdownStyles.placeholder}>{placeholder}</Text>
              )}
            </View>
          </View>
          <View style={dropdownStyles.arrowIcon}>
            <Ionicons
              name={isOpen ? "chevron-up-outline" : "chevron-down-outline"}
              size={18}
              color="#999"
            />
          </View>
        </TouchableOpacity>
      }


      {isOpen && (
        <View style={dropdownStyles.dropdown}>
          <View style={dropdownStyles.scrollView} >
            {options.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  dropdownStyles.option,
                  value && option[valueKey] === (typeof value === 'string' ? value : value[valueKey]) &&
                  dropdownStyles.selectedOption
                ]}
                onPress={() => handleOptionSelect(option)}
              >
                <Text
                  style={[
                    dropdownStyles.optionText,
                    value && option[valueKey] === (typeof value === 'string' ? value : value[valueKey]) &&
                    dropdownStyles.selectedOptionText
                  ]}
                >
                  {option[labelKey]}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};
const TextField = ({ label = "", initialValue = "", onChange, placeholder = "", imageName = "", style = {}, disabled = false }) => {
  const [value, setValue] = useState(initialValue);
  const [focused, setFocused] = useState(false);
  const inputRef = useRef(null);

  // Update state when initialValue prop changes
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  const handleChange = (e) => {
    setValue(e.target.value);
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const handleContainerClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <TouchableOpacity
      style={[dateStyles.field, focused && dateStyles.fieldFocused, style]}
      activeOpacity={0.9}
      onPress={handleContainerClick}
    >
      <View style={dateStyles.fieldIcon}>
        {/* <Ionicons name={iconname} size={18} color="#999" /> */}
        <Image source={imageMap[imageName]} />
      </View>
      <View style={dateStyles.fieldContent}>
        <Text style={dateStyles.fieldLabel}>{label}</Text>
        <View style={dateStyles.inputContainer}>
          {/* The input is always visible but styled to match design */}
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handleChange}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={placeholder}
            disabled={disabled}
            style={{
              fontSize: 14,
              color: '#000',
              padding: 0,
              border: 'none',
              outline: 'none',
              width: '100%',
              height: '100%',
              backgroundColor: 'transparent',
              fontFamily: "Poppins_500Medium",
              // Show placeholder with correct styling
              '::placeholder': {
                color: '#aaa',
                opacity: 1,
              }
            }}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};
const DatePickerField = ({ label = "DOB", initialValue = "", onChange, placeholder = "Select date", imageName = "" }) => {
  const [value, setValue] = useState("");
  const [focused, setFocused] = useState(false);
  const inputRef = React.useRef(null);
  const handleChange = (e) => {
    setValue(e.target.value);
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const getFormattedDate = () => {
    if (!value) return "";

    const date = new Date(value);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: '2-digit',
      year: 'numeric'
    });
  };

  const handleContainerClick = () => {
    if (inputRef.current) {
      inputRef.current.click();
      inputRef.current.focus();
    }
  };

  const openDatePicker = () => {
    if (inputRef.current) {
      inputRef.current.showPicker();
    }
  };
  useEffect(() => {
    setValue(initialValue)
  })
  return (
    <TouchableOpacity
      style={[dateStyles.field, focused && dateStyles.fieldFocused]}
      onPress={handleContainerClick}
      activeOpacity={0.9}
    >
      <View style={dateStyles.fieldIcon}>
        {/* <Ionicons name="calendar-outline" size={18} color="#999" /> */}
        <Image source={imageMap[imageName]} />
      </View>
      <TouchableOpacity onPress={openDatePicker}>
        <View style={dateStyles.fieldContent}>
          <Text style={dateStyles.fieldLabel}>{label}</Text>
          <View style={dateStyles.inputContainer}>
            {initialValue ? (
              <Text style={dateStyles.fieldInput}>{getFormattedDate()}</Text>
            ) : (
              <Text style={dateStyles.placeholder}>{placeholder}</Text>
            )}
            <input
              ref={inputRef}
              type="date"
              value={value}
              onChange={handleChange}
              onFocus={() => setFocused(true)}
              onBlur={() => setFocused(false)}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                opacity: 0,
                cursor: 'pointer',
                zIndex: 2,
              }}
            />
          </View>
        </View>

      </TouchableOpacity>

    </TouchableOpacity>
  );
};
const PhoneTextField = ({ label = "", initialValue = "", onChange, placeholder = "", imageName = "", style = {} }) => {
  const [value, setValue] = useState(initialValue);
  const [focused, setFocused] = useState(false);
  const inputRef = useRef(null);

  // Format phone number as user types
  const formatPhoneNumber = (input) => {
    // Strip all non-numeric characters
    const phoneNumber = input.replace(/\D/g, '');

    // Format based on input length
    if (phoneNumber.length < 4) {
      return phoneNumber;
    } else if (phoneNumber.length < 7) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
    } else {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    }
  };

  // Update state when initialValue prop changes
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  const handleChange = (e) => {
    const formattedValue = formatPhoneNumber(e.target.value);
    setValue(formattedValue);
    if (onChange) {
      onChange(formattedValue);
    }
  };

  const handleContainerClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <TouchableOpacity
      style={[dateStyles.field, focused && dateStyles.fieldFocused, style]}
      activeOpacity={0.9}
      onPress={handleContainerClick}
    >
      <View style={dateStyles.fieldIcon}>
        <Image source={imageMap[imageName]} />
        {/* <Ionicons name={iconname} size={18} color="#999" /> */}
      </View>
      <View style={dateStyles.fieldContent}>
        <Text style={dateStyles.fieldLabel}>{label}</Text>
        <View style={dateStyles.inputContainer}>
          <input
            ref={inputRef}
            type="tel"
            value={value}
            onChange={handleChange}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={placeholder}
            style={{
              fontSize: 14,
              color: '#000',
              padding: 0,
              border: 'none',
              outline: 'none',
              width: '100%',
              height: '100%',
              fontFamily: "Poppins_500Medium",
              backgroundColor: 'transparent',
              '::placeholder': {
                color: '#aaa',
                opacity: 1,
              }
            }}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Phone Input for Native Mobile
const PhoneTextInput = ({ label = "Mobile", value, onChangeText, placeholder = "Add Mobile" }) => {
  // Format phone number as user types
  const formatPhoneNumber = (input) => {
    // Strip all non-numeric characters
    const phoneNumber = input.replace(/\D/g, '');

    // Format based on input length
    if (phoneNumber.length < 4) {
      return phoneNumber;
    } else if (phoneNumber.length < 7) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
    } else {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    }
  };

  const handleChangeText = (text) => {
    const formattedValue = formatPhoneNumber(text);
    onChangeText(formattedValue);
  };

  return (
    // <View style={styles.field}>
    //   <View style={styles.fieldContent}>
    <TextInput
      mode="outlined"
      label={label}
      style={styles.mobileTextField}
      value={value}
      onChangeText={handleChangeText}
      left={<TextInput.Icon icon={() => <Ionicons name="call-outline" size={20} color="#999" />} />}
      outlineColor="white"
      keyboardType="phone-pad"
      placeholder={placeholder}
    />
    //   </View>
    // </View>
  );
};

const MyAccount = () => {
  setPageTitle('My Profile');
  const isWeb = Platform.OS === 'web';
  const [date, setDate] = useState(null);
  const [isPickerVisible, setPickerVisible] = useState(false);
  const [tempDate, setTempDate] = useState(new Date());

  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString();
  };

  const navigation = useNavigation();

  const [fullName, setFullName] = useState<any>('')
  const [firstName, setFirstName] = useState<any>('')
  const [lastName, setLastName] = useState<any>('')
  const [discipline, setDiscipline] = useState<any>('')
  const [email, setEmailId] = useState('')
  const [phoneNo, setPhoneNo] = useState('')
  const [clinicianId, setClinicianId] = useState('')
  // const [dob, setDob] = useState('');
  const [dob, setDob] = useState<Date | null>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [allDiscipline, setAllDiscipline] = useState([])

  const footerHeight = Platform.OS === 'ios' ? 80 : 60;

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success")
  const insets = useSafeAreaInsets();
  //setDob(dateOfBirth ? new Date(dateOfBirth) : null);

  const formatDateMMDDYYYY = (date: Date | null) => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) return '';
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  };
  const fetchUserDetails = async () => {

    const email = await getSecureItem("email");
    const firstname = await getSecureItem("firstName");
    const lastname = await getSecureItem("lastName");
    const clinicianId = await getSecureItem("staffId");
    const discipline = await getSecureItem("discipline");
    const phoneNo = await getSecureItem("phone");
    const dateOfBirth = await getSecureItem("dateOfBirth");

    setFullName(`${firstname || ''} ${lastname || ''}`)
    setFirstName(`${firstname || ''}`)
    setLastName(`${lastname || ''}`)
    setDiscipline(`${discipline || ''}`)
    setEmailId(`${email || ''}`)
    setClinicianId(`${clinicianId || ''}`)
    // setDob(`${dateOfBirth || ''}`)
    if (dateOfBirth) {
      let dateObj = null;

      // Try different date parsing approaches
      if (dateOfBirth.includes('/')) {
        // Format: MM/DD/YYYY or DD/MM/YYYY
        const parts = dateOfBirth.split('/');
        if (parts.length === 3) {
          // Assuming MM/DD/YYYY format
          dateObj = new Date(parts[2], parts[0] - 1, parts[1]);
        }
      } else if (dateOfBirth.includes('-')) {
        // Format: YYYY-MM-DD or DD-MM-YYYY
        dateObj = new Date(dateOfBirth);
      } else {
        // Try direct conversion
        dateObj = new Date(dateOfBirth);
      }

      if (dateObj && !isNaN(dateObj.getTime())) {
        setDob(dateObj);
        console.log("Successfully parsed date:", dateObj);
      } else {
        console.log("Could not parse date:", dateOfBirth);
        setDob(null);
      }
    } else {
      setDob(null);
    }


    // setDob(dateOfBirth ? new Date(dateOfBirth) : null);
    // console.log("dateOfBirth--->", dateOfBirth)
    const formattedPhone = phoneNo ? formatPhoneNumber(phoneNo) : '';
    //const formattedPhone = "9800387761"
    setPhoneNo(formattedPhone);
  }

  const fetchAllDiscipline = () => {
    fetchDiscipline()
      .then((result: any) => {
        if (result.status == "ok") {
          let disciplines = result.data.map((dis) => {
            return { label: dis.name, value: dis._id }
          })
          setAllDiscipline(disciplines)
        }
      })
      .catch((error) => {
        console.log(error);
      })
  }
  useEffect(() => {
    fetchUserDetails()
    fetchAllDiscipline()
  }, [])


  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date) => {
    setDob(date.toISOString().split('T')[0]); // Format as YYYY-MM-DD
    hideDatePicker();
  };

  const renderBackButton = () => {
    return (
      <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
        {/* <ChevronLeft color={theme.colors.surface} size={24} /> */}
        <Feather name="chevron-left" size={20} color={theme.colors.surface} />

      </TouchableOpacity>
    );
  };

  const onBackClick = () => {
    // console.log("Back clicked");
    navigation.goBack();
  };

  const saveProfileInfo = () => {
    if (!validateEmail(email)) {
      setShowMessageModal(true)
      setMessage("Invalid Email Address.Please provide valid Email.")
      setMessageType("error")
    } else if (!validateMobile(phoneNo)) {
      setShowMessageModal(true)
      setMessage("Invalid Mobile Number.Please provide valid mobile with this format (************* .")
      setMessageType("error")
    } else {
      const data = { firstName: firstName, lastName: lastName, email: email, phone: phoneNo, clinicinaId: clinicianId, dateOfBirth: dob }
      console.log("data will be --->", data)
      setIsLoading(true)
      saveUserData(data)
        .then((result: any) => {
          console.log("result-->", result)
          if (result.status == "ok") {
            console.log("result-ok->", result)

            saveToCache(data)
            // alert("Profile Updated successfully.")
            setShowMessageModal(true)
            setMessage("Profile Updated successfully.")
            setMessageType("success")
          } else {
            console.log("result-notok->", result)

            setShowMessageModal(true)
            setMessage(result.errorMessage)
            setMessageType("error")
          }
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => setIsLoading(false));
    }

  };
  const saveToCache = async (data: any) => {
    try{
    // console.log("data--->", JSON.stringify(data))
    await setSecureItem("firstName", data.firstName);
    await setSecureItem("lastName", data.lastName);
    await setSecureItem("staffId", data.clinicinaId);
    await setSecureItem("email", data.email);
   // await setSecureItem("discipline", data.disciplineId);
    await setSecureItem("phone", data.phone);
    // console.log("get phone-->", await getSecureItem("phone"))
    // await setSecureItem("dateOfBirth", data.dateOfBirth);
    // Fix: Store date properly
    if (data.dateOfBirth) {
      const dateToStore = data.dateOfBirth instanceof Date
        ? data.dateOfBirth.toISOString()
        : data.dateOfBirth;
      await setSecureItem("dateOfBirth", dateToStore);
    }
    setFullName(`${data.firstName || ''} ${data.lastName || ''}`)
  }catch(e){
        console.log("e--->", e)

  }
  }
  const renderNativeContent = () => {
    return (
      <ScrollView style={styles.scrollView}>

        {/* <View style={styles.card}> */}
        <Text allowFontScaling={false} style={styles.cardTitle}>Personal Information</Text>

        <View style={[styles.field, { marginTop: 15 }]}>
          <View style={styles.fieldContent}>
            <TextInput
              mode="outlined"
              style={styles.mobileTextField}
              label="First Name"
              value={firstName}
              onChangeText={(val) => setFirstName(val)}
              left={<TextInput.Icon icon={() => <Ionicons name="person-outline" size={20} color="#999" />} />}
              outlineColor="white"
            />
          </View>
        </View>

        <View style={styles.field}>
          <View style={styles.fieldContent}>
            <TextInput
              mode="outlined"
              style={styles.mobileTextField}
              label="Last Name"
              value={lastName}
              onChangeText={(val) => setLastName(val)}
              left={<TextInput.Icon icon={() => <Ionicons name="person-outline" size={20} color="#999" />} />}
              outlineColor="white"
            />
          </View>
        </View>

        <View style={styles.field}>
          <View style={styles.fieldContent}>
            <TextInput
              mode="outlined"
              style={styles.mobileTextField}
              label="Discipline"
              value={discipline}
              // onChangeText={(val) => setDiscipline(val)}
              left={<TextInput.Icon icon={() => <Ionicons name="person-outline" size={20} color="#999" />} />}
              outlineColor="white"
              disabled={true}

            />
          </View>
        </View>

        <View style={styles.field}>
          <View style={styles.fieldContent}>
            <TextInput
              mode="outlined"
              style={styles.mobileTextField}
              label="Email"
              value={email}
              onChangeText={(val) => setEmailId(val)}
              left={<TextInput.Icon icon={() => <Ionicons name="mail-outline" size={20} color="#999" />} />}
              outlineColor="white"

            />
          </View>
        </View>

        <View style={styles.field}>
          <View style={styles.fieldContent}>
            <PhoneTextInput
              label="Mobile"
              value={phoneNo}
              onChangeText={setPhoneNo}
              placeholder="(*************"
            />
          </View>
        </View>

        <View style={styles.field}>
          <View style={styles.fieldContent}>
            <TextInput
              mode="outlined"
              style={[styles.mobileTextField]}
              label="Clinician ID"
              value={clinicianId}
              onChangeText={(val) => setClinicianId(val)}
              left={<TextInput.Icon icon={() => <Ionicons name="card-outline" size={20} color="#999" />} />}
              outlineColor="white"
              disabled={true}
            />
          </View>
        </View>

        <View style={styles.field}>
          <View style={styles.fieldContent}>
            <TextInput
              mode="outlined"
              label="DOB"
              style={styles.mobileTextField}
              value={formatDateMMDDYYYY(dob)} // This will now properly show the date
              left={<TextInput.Icon
                icon={() => <Ionicons name="calendar-outline" size={20} color="#999" />}
                onPress={() => {
                  setTempDate(dob || new Date());
                  setPickerVisible(true);
                }}
              />}
              outlineColor="white"
              editable={false}
            />

            {/* <DateTimePickerModal
              isVisible={isDatePickerVisible}
              mode="date"
              onConfirm={handleConfirm}
              onCancel={hideDatePicker}
            /> */}
            <Modal
              visible={isPickerVisible}
              transparent={true}
              animationType="slide"
              onRequestClose={() => setPickerVisible(false)}
            >
              <View style={styles.modalOverlay}>
                <TouchableOpacity
                  style={styles.overlayTouchable}
                  activeOpacity={1}
                  onPress={() => setPickerVisible(false)}
                >
                  <View style={styles.bottomSheet}>
                    <TouchableOpacity activeOpacity={1}>
                      <View style={styles.bottomSheetHeader}>
                        <TouchableOpacity onPress={() => setPickerVisible(false)}>
                          <Text allowFontScaling={false} style={styles.cancelButton}>Cancel</Text>
                        </TouchableOpacity>
                        <Text allowFontScaling={false} style={styles.headerTitles}>Select Date</Text>
                        <TouchableOpacity onPress={() => {
                          setDob(tempDate);
                          setPickerVisible(false);
                        }}>
                          <Text allowFontScaling={false} style={styles.confirmButton}>Confirm</Text>
                        </TouchableOpacity>
                      </View>
                      <View style={styles.datePickerContainer}>
                        <DateTimePicker
                          value={tempDate}
                          mode="date"
                          display="spinner"
                          maximumDate={new Date()} 
                          onChange={(event, selectedDate) => {
                            if (selectedDate) setTempDate(selectedDate);
                          }}
                        />
                      </View>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              </View>
            </Modal>

          </View>
        </View>
        <TouchableOpacity style={styles.saveButton} onPress={() => { saveProfileInfo() }} >
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
        {/* </View> */}
      </ScrollView>
    )
  }
  const renderWebContent = () => {
    return (
      <div style={styles.scrollViewWeb}>

        {/* <View style={styles.card}> */}
        <Text style={styles.cardTitle}>Personal Information</Text>
        <TextField style={{ marginTop: 15 }}
          label="First Name"
          initialValue={firstName}
          onChange={setFirstName}
          placeholder="Add First Name"
          // iconname="person-outline"
          imageName='profile'
        />
        <TextField
          label="Last Name"
          initialValue={lastName}
          onChange={setLastName}
          placeholder="Add Last Name"
          imageName='profile'
        />

        <Dropdown
          label="Discipline"
          options={allDiscipline}
          initialValue={discipline}
          imageName='profile'
          onChange={(selectedOption) => setDiscipline(selectedOption)}
          disabled={true}
        />
        <TextField
          label="Email"
          initialValue={email}
          onChange={setEmailId}
          placeholder="Add Email"
          imageName='email'
        />
        <PhoneTextField
          label="Mobile"
          initialValue={phoneNo}
          onChange={setPhoneNo}
          placeholder="(*************"
          imageName='mobile'
        />
        <TextField
          label="Clinician ID"
          initialValue={clinicianId}
          onChange={setClinicianId}
          placeholder="Add Clinician ID"
          imageName='badge'
          style={styles.disabled}
          disabled={true}
        />
        <DatePickerField
          label="DOB"
          initialValue={dob}
          onChange={setDob}
          placeholder="Select date"
          imageName='date'
        />

        {/* </View> */}
        <TouchableOpacity style={styles.saveButton} onPress={() => { saveProfileInfo() }} >
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </div>
    )
  }

  const onModalCancel = () => {
    setShowMessageModal(false)
  };
  return (

    <View style={styles.container}>
      {/* <StatusBar barStyle="dark-content" backgroundColor="#3B81F6" /> */}
      <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
      <FullScreenLoader visible={isLoading} />
      <View style={[styles.headerContainer, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          {renderBackButton()}
          <View >
            <Text allowFontScaling={false} style={styles.headerText}>My Profile</Text>
          </View>
        </View>

        <View style={styles.profileHeader}>
          <View style={styles.profileBox}>
            <Text allowFontScaling={false} style={styles.profileInitials}>
              {firstName != "" && firstName[0]}{lastName != "" && lastName[0]}
            </Text>
          </View>
          {/* <Image
              source={{ uri: 'https://randomuser.me/api/portraits/women/52.jpg' }}
              style={styles.profileImage}
            /> */}
          <Text allowFontScaling={false} style={styles.profileName}>{fullName}</Text>
        </View>
      </View>

      <View style={styles.content}>
        {
          isWeb ? renderWebContent() : renderNativeContent()
        }

      </View>
    </View>
  );
};

const dropdownStyles = StyleSheet.create({

  container: {
    position: 'relative',
    zIndex: 1,
    marginBottom: 10,
  },
  field: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 6,
    borderRadius: 10,
    backgroundColor: 'white',
    alignItems: 'center',
  },
  fieldFocused: {
    borderColor: '#d0d0ff',
    backgroundColor: '#fafafe',
  },
  fieldIcon: {
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  fieldContent: {
    flex: 1,
    position: 'relative',
  },
  fieldLabel: {
    fontSize: 12,
    color: '#888',
    // marginBottom: 4,
  },
  inputContainer: {
    position: 'relative',
    height: 14,
    justifyContent: 'center',
  },
  fieldInput: {
    fontSize: 14,
    color: '#000',
    padding: 0,
    fontFamily: "Poppins_500Medium",
  },
  placeholder: {
    fontSize: 16,
    color: '#aaa',
    padding: 0,
  },
  arrowIcon: {
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d0d0ff',
    borderRadius: 10,
    marginTop: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 2,
  },
  scrollView: {
    maxHeight: 180,
    overflow: 'auto'

  },
  option: {
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  selectedOption: {
    backgroundColor: '#f0f0ff',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedOptionText: {
    color: '#5050ff',
    fontWeight: '500',
  },
})
const dateStyles = StyleSheet.create({

  field: {
    flexDirection: 'row',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 6,
    borderRadius: 10,
    backgroundColor: 'white',
    alignItems: 'center',
  },
  fieldFocused: {
    borderColor: '#d0d0ff',
    backgroundColor: '#fafafe',
  },
  fieldIcon: {
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  fieldContent: {
    flex: 1,
    position: 'relative',
  },
  fieldLabel: {
    fontSize: 12,
    color: '#808080',
    // marginBottom: 4,
    fontFamily: 'Poppins_400Regular'
  },
  inputContainer: {
    position: 'relative',
    height: 14,
    justifyContent: 'center',
  },
  fieldInput: {
    fontSize: 14,
    color: '#000',
    padding: 0,
    fontFamily: "Poppins_500Medium",
  },
  placeholder: {
    fontSize: 16,
    color: '#aaa',
    padding: 0,
  },
  calendarButton: {
    padding: 4,
  }
});
const styles = StyleSheet.create({
  mobileTextField: {
    backgroundColor: 'white'
  },
  disabled:
    { "backgroundColor": '#EFF3F6' },
  profileBox: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#CBD5E1",
    justifyContent: "center",
    alignItems: "center",
    // marginRight: 6,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 25,
  },
  fieldContainer: {
    flexDirection: "row", // Align items horizontally
    alignItems: "center", // Align items in center vertically
    padding: 10,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 5,
    backgroundColor: "#fff",
    width: "90%", // Adjust width as needed
    marginBottom: 10,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    // height:'80vh',
    // overflow:'auto'
  },
  headerContainer: {
    backgroundColor: '#3B81F6',
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
  },
  headerText: {
    color: "white",
    fontSize: 20,
    fontFamily: 'Poppins_500Medium'
  },
  backButton: {
    // padding: 5,
    color: 'white',
    paddingLeft:5,
    paddingRight:10,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 24,
  },

  scrollView: {
    flex: 1,

    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
    ...globalStyles.shadow,
    marginBottom: 30,
    marginTop: -50, // Pull the scrollView up to create overlap
  },

  scrollViewWeb: {
    height: "60vh",
    overflow: "auto",
    backgroundColor: 'white',
    borderRadius: 10,
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 30,
    marginTop: -50, // Pull the scrollView up to create overlap
    boxShadow: '0px 0px 6px rgba(0, 0, 0, 0.2), 0px 0px 1px rgba(0, 0, 0, 0.19)',

  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 50,
    paddingHorizontal: 20,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: 'white',
  },
  profileName: {
    color: 'white',
    fontSize: 20,
    fontFamily: 'Poppins_500Medium',
    marginLeft: 15,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 16,
    ...globalStyles.shadow,
    marginBottom: 30,
  },
  cardTitle: {
    fontSize: 20,
    fontFamily: 'Poppins_600SemiBold'
  },
  field: {
    flexDirection: 'row',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    // paddingBottom: 10,
    paddingHorizontal: 6,
    paddingVertical: 6,
    borderRadius: 10,
    // padding:10,
  },
  fieldIcon: {
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  fieldContent: {
    flex: 1,
  },
  fieldLabel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  fieldInput: {
    fontSize: 16,
    color: '#333',
    padding: 0,
  },
  saveButton: {
    backgroundColor: '#4F47E5',
    borderRadius: 25,
    paddingVertical: 14,
    marginHorizontal: 16,
    marginBottom: 20,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  tabBar: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    backgroundColor: 'white',
    paddingVertical: 10,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 5,
  },
  tabLabel: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  activeTab: {
    borderTopWidth: 0,
  },
  activeTabLabel: {
    color: '#4a7df6',
  },

  webPickerContainer: {
    position: 'relative',
  },
  textInput: {
    backgroundColor: 'transparent',
  },
  invisibleInput: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0,
    zIndex: 1,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  overlayTouchable: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    maxHeight: Dimensions.get('window').height * 0.6,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cancelButton: {
    color: '#6B7280',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  headerTitles: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    fontFamily: 'Poppins_600SemiBold',
  },
  confirmButton: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  datePickerContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    alignItems: 'center',
  },
  datePicker: {
    width: '100%',
    height: 200,
  },
  selectedDateContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  selectedDateLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Poppins_400Regular',
    marginBottom: 4,
  },
  selectedDateText: {
    fontSize: 18,
    color: '#1F2937',
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 20,
    gap: 12,
  },
  cancelButtonContainer: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  confirmButtonContainer: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    borderRadius: 30,
    padding: 12,
    alignItems: 'center',
  },

  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },

});


export default MyAccount;
