import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Switch,
  SafeAreaView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
// import {
//   ChevronLeft,
//   Calendar,
//   Plus,
//   ArrowLeft,
//   ChevronDown,
// } from 'lucide-react-native';
import { StatusBar } from 'expo-status-bar';
import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';
import DateTimePicker from '@react-native-community/datetimepicker';
import { DatePickerModal } from 'react-native-paper-dates';
import theme from 'src/theme';

// Dynamic form configuration
const formConfig = {
  staffId: {
    label: 'Clinician Number',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Personal Information',
  },
  firstName: {
    label: 'First Name',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Personal Information',
  },
  lastName: {
    label: 'Last Name',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Personal Information',
  },
  jobTitleDesignation: {
    label: 'Job Title',
    isRequired: true,
    questionType: 'DROPDOWN',
    options: [
      'Therapist',
      'Consultant',
      'Supervisor',
      'Coordinator',
      'Manager',
    ],
    fieldGroupName: 'Job Information',
  },
  discipline: {
    label: 'Discipline',
    isRequired: true,
    questionType: 'DROPDOWN',
    options: ['PT', 'OT', 'SLP'],
    fieldGroupName: 'Job Information',
  },
  status: {
    label: 'Status',
    isRequired: true,
    questionType: 'DROPDOWN',
    options: ['Active', 'Inactive'],
    fieldGroupName: 'Job Information',
  },
  dob: {
    label: 'Date of Birth',
    isRequired: true,
    questionType: 'DATE_PICKER',
    fieldGroupName: 'Personal Information',
  },
  gender: {
    label: 'Gender',
    isRequired: true,
    questionType: 'RADIO',
    options: ['Male', 'Female'],
    fieldGroupName: 'Personal Information',
  },
  address1: {
    label: 'Address 1',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Contact Information',
  },
  address2: {
    label: 'Address 2',
    isRequired: false,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Contact Information',
  },
  city: {
    label: 'City',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Contact Information',
  },
  state: {
    label: 'State',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Contact Information',
  },
  zip: {
    label: 'Zip Code',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Contact Information',
  },
  primaryPhone: {
    label: 'Primary Phone',
    isRequired: true,
    questionType: 'TEXT_INPUT',
    fieldGroupName: 'Contact Information',
  },
};

export default function CreateStaffScreen({ navigation }) {
  const initializeFormState = () => {
    const initialState = {};
    Object.keys(formConfig).forEach(fieldKey => {
      const field = formConfig[fieldKey];

      if (field.defaultValue) {
        initialState[fieldKey] = field.defaultValue;
      } else {
        switch (field.questionType) {
          case 'TEXT_INPUT':
            initialState[fieldKey] = '';
            break;
          case 'CHECKBOX':
            initialState[fieldKey] = '';
            break;
          case 'DROPDOWN':
            initialState[fieldKey] = '';
            break;
          case 'RADIO':
            initialState[fieldKey] = '';
            break;
          case 'DATE_PICKER':
            initialState[fieldKey] = '';
            break;
          default:
            initialState[fieldKey] = '';
        }
      }
    });
    return initialState;
  };

  const [formData, setFormData] = useState(initializeFormState);
  const [dropdownOpen, setDropdownOpen] = useState({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [activeField, setActiveField] = useState(null);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    const newErrors = {};
    let isValid = true;

    Object.keys(formConfig).forEach(fieldKey => {
      const field = formConfig[fieldKey];

      if (field.isRequired) {
        if (
          !formData[fieldKey] ||
          (typeof formData[fieldKey] === 'string' &&
            formData[fieldKey].trim() === '')
        ) {
          newErrors[fieldKey] = `${field.label} is required`;
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const updateErrors = fieldKey => {
    if (errors[fieldKey]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    }
  };

  // Group fields by fieldGroupName
  const groupFields = () => {
    const groups = {};

    Object.entries(formConfig).forEach(([fieldId, fieldConfig]) => {
      const { fieldGroupName } = fieldConfig;
      if (!groups[fieldGroupName]) {
        groups[fieldGroupName] = [];
      }
      groups[fieldGroupName].push({ fieldId, ...fieldConfig });
    });

    return groups;
  };

  const handleInputChange = (fieldId, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value,
    }));
    updateErrors(fieldId);
  };

  const toggleDropdown = fieldId => {
    setDropdownOpen(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId],
    }));
    updateErrors(fieldId);
  };

  const selectDropdownOption = (fieldId, option) => {
    handleInputChange(fieldId, option);
    toggleDropdown(fieldId);
    updateErrors(fieldId);
  };

  const handleDateChange = params => {
    setShowDatePicker(false);
    if (params?.date && activeField) {
      handleInputChange(activeField, params.date.toISOString().split('T')[0]);
    }
  };

  const showDatePickerFor = fieldId => {
    setActiveField(fieldId);
    setShowDatePicker(true);
    updateErrors(fieldId);
  };

  const handleSubmit = () => {
    // // Validate and submit form data
    // console.log('Submitting form data:', formData);
    // // You would add validation logic here
    // alert('Clinician added successfully!');
    // navigation.goBack();

    if (validateForm()) {
      setIsSubmitting(true);

      console.log(formData);

      // // Simulate API call
      // setTimeout(() => {
      //   setIsSubmitting(false);
      //   // Navigate to success page or show success message
      //   alert('Clinician created successfully!');
      //   // Reset form
      //   setFormData(initializeFormState());
      // }, 2000);
    } else {
      console.log(formData);
      alert('Please enter all required fields');
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderField = field => {
    const { fieldId, label, isRequired, questionType, options, hasAddButton } =
      field;

    switch (questionType) {
      case 'TEXT_INPUT':
        return (
          <View style={styles.inputContainer} key={fieldId}>
            <Text style={styles.inputLabel}>
              {label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TextInput
              style={styles.textInput}
              value={formData[fieldId] || ''}
              onChangeText={text => handleInputChange(fieldId, text)}
              placeholder={`Enter ${label}`}
              placeholderTextColor={'#6B7280'}
            />
            {errors[fieldId] && (
              <Text style={styles.errorText}>{errors[fieldId]}</Text>
            )}
          </View>
        );

      case 'DROPDOWN':
        return (
          <View style={styles.inputContainer} key={fieldId}>
            <Text style={styles.inputLabel}>
              {label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => toggleDropdown(fieldId)}
            >
              <Text
                style={
                  formData[fieldId]
                    ? styles.dropdownButtonText
                    : { color: '#6B7280' }
                }
              >
                {formData[fieldId] || `Select ${label}`}
              </Text>
              {/* <ChevronDown size={20} color="#6B7280" /> */}
            </TouchableOpacity>

            {dropdownOpen[fieldId] && (
              <View style={styles.dropdownMenu}>
                {options.map(option => (
                  <TouchableOpacity
                    key={option}
                    style={styles.dropdownItem}
                    onPress={() => selectDropdownOption(fieldId, option)}
                  >
                    <Text style={styles.dropdownItemText}>{option}</Text>
                  </TouchableOpacity>
                ))}
                {/*{hasAddButton && (*/}
                {/*  <TouchableOpacity style={styles.addOptionButton}>*/}
                {/*    <Plus size={16} color="#4C51BF" />*/}
                {/*    <Text style={styles.addOptionText}>Add New Option</Text>*/}
                {/*  </TouchableOpacity>*/}
                {/*)}*/}
              </View>
            )}

            {errors[fieldId] && (
              <Text style={styles.errorText}>{errors[fieldId]}</Text>
            )}
          </View>
        );

      case 'DATE_PICKER':
        return (
          <View style={styles.inputContainer} key={fieldId}>
            <Text style={styles.inputLabel}>
              {label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => showDatePickerFor(fieldId)}
            >
              <TextInput
                style={styles.datePickerTextInput}
                value={formData[fieldId] || ''}
                // onChangeText={text => handleInputChange(fieldId, text)}
                placeholder={`Enter ${label}`}
                editable={false}
                placeholderTextColor={'#6B7280'}
              />
              {/* <Calendar size={20} color="#6B7280" /> */}
            </TouchableOpacity>
            {showDatePicker && (
              <DatePickerModal
                mode="single"
                visible={showDatePicker}
                onDismiss={() => setShowDatePicker(false)}
                date={
                  formData[fieldId] ? new Date(formData[fieldId]) : new Date()
                }
                onConfirm={handleDateChange}
              />
            )}
            {errors[fieldId] && (
              <Text style={styles.errorText}>{errors[fieldId]}</Text>
            )}
          </View>
        );

      case 'RADIO':
        return (
          <View style={styles.inputContainer} key={fieldId}>
            <Text style={styles.inputLabel}>
              {label} {isRequired && <Text style={styles.requiredStar}>*</Text>}
            </Text>
            <View style={styles.radioGroup}>
              {options.map(option => (
                <TouchableOpacity
                  key={option}
                  style={styles.radioOption}
                  onPress={() => handleInputChange(fieldId, option)}
                >
                  <View style={styles.radioButton}>
                    {formData[fieldId] === option && (
                      <View style={styles.radioButtonSelected} />
                    )}
                  </View>
                  <Text style={styles.radioLabel}>{option}</Text>
                </TouchableOpacity>
              ))}
            </View>
            {errors[fieldId] && (
              <Text style={styles.errorText}>{errors[fieldId]}</Text>
            )}
          </View>
        );

      default:
        return null;
    }
  };

  const groupedFields = groupFields();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header */}
      <View style={styles.headerBar}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          {/* <ChevronLeft color="#1F2937" size={24} /> */}
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Clinician</Text>

        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <LinearGradient
            colors={['#4C51BF', '#6B46C1']}
            style={styles.submitGradient}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.submitButtonText}>Save Clinician</Text>
            )}
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* Form */}
      <ScrollView
        style={styles.formContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formInnerContainer}>
          <View style={{ width: '50%' }}>
            {Object.entries(groupedFields).map(([groupName, fields]) => (
              <View key={groupName} style={styles.fieldGroup}>
                <Text style={styles.groupTitle}>{groupName}</Text>
                {fields.map(field => renderField(field))}
              </View>
            ))}
            <View style={{ height: 40 }} />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  headerBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    position: 'relative',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#1F2937',
  },
  placeholder: {
    width: 40,
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  formInnerContainer: {
    height: screenHeight - 90,
    width: screenWidth,
    alignItems: 'center',
  },
  fieldGroup: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  groupTitle: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    color: '#4B5563',
    marginBottom: 8,
  },
  requiredStar: {
    color: '#EF4444',
  },
  textInput: {
    height: 48,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontFamily: 'Poppins_400Regular',
    backgroundColor: '#FFFFFF',
  },
  dropdownButton: {
    height: 48,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
  },
  dropdownButtonText: {
    fontFamily: 'Poppins_400Regular',
  },
  dropdownMenu: {
    marginTop: 4,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  dropdownItemText: {
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
  },
  addOptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  addOptionText: {
    fontFamily: 'Poppins_500Medium',
    color: '#4C51BF',
    marginLeft: 8,
  },
  datePickerButton: {
    height: 48,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    fontFamily: 'Poppins_400Regular',
    color: '#6B7280',
  },
  datePickerTextInput: {
    width: '100%',
    fontFamily: 'Poppins_400Regular',
  },
  datePickerText: {
    fontFamily: 'Poppins_400Regular',
  },
  radioGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
    marginBottom: 8,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#6B7280',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4C51BF',
  },
  radioLabel: {
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    marginBottom: 40,
  },
  cancelButton: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    backgroundColor: '#FFFFFF',
  },
  cancelButtonText: {
    fontFamily: 'Poppins_500Medium',
    color: '#4B5563',
  },
  submitButton: {
    flex: 1,
    height: 36,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'absolute',
    right: 24,
  },
  submitGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 16,
    paddingRight: 16,
  },
  submitButtonText: {
    fontFamily: 'Poppins_500Medium',
    color: '#FFFFFF',
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    marginTop: 4,
  },
});
