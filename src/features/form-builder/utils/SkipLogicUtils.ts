import { FormLayoutComponentChildrenType, FormLayoutComponentsType } from '../types/FormTemplateTypes';

export interface SkipLogicConfig {
    targetQuestionId: string;
    targetQuestionCode?: string;
    targetQuestionLabel?: string;
}

export interface SkipLogicState {
    hiddenQuestions: Set<string>;
    skipLogicAnswers: Record<string, string>;
}

/**
 * Applies skip logic when a question is answered
 * @param questionCode - The code of the question that was answered
 * @param selectedValue - The value that was selected
 * @param categories - All form categories and questions
 * @param currentState - Current skip logic state
 * @returns Updated skip logic state
 */
export const applySkipLogic = (
    questionCode: string,
    selectedValue: string,
    categories: FormLayoutComponentsType[],
    currentState: SkipLogicState
): SkipLogicState => {
    const newHiddenQuestions = new Set(currentState.hiddenQuestions);
    const newSkipLogicAnswers = { ...currentState.skipLogicAnswers };

    // Find the question that was answered
    let targetQuestion: FormLayoutComponentChildrenType | null = null;
    for (const category of categories) {
        const question = category.items.find(q => q.questionCode === questionCode);
        if (question && question.skipLogic) {
            targetQuestion = question;
            break;
        }
    }

    if (targetQuestion && targetQuestion.skipLogic && targetQuestion.skipLogic[selectedValue]) {
        const skipConfig = targetQuestion.skipLogic[selectedValue];
        const targetQuestionCode = skipConfig.targetQuestionCode;

        // Hide all questions between the current question and the target question
        let shouldHide = false;
        for (const category of categories) {
            for (const question of category.items) {
                if (!question.isContainer) {
                    if (question.questionCode === questionCode) {
                        shouldHide = true;
                    } else if (question.questionCode === targetQuestionCode) {
                        shouldHide = false;
                        break;
                    } else if (shouldHide) {
                        newHiddenQuestions.add(question.questionCode);
                    }
                }
            }
        }

        // Store the skip logic answer
        newSkipLogicAnswers[questionCode] = selectedValue;
    } else {
        // If no skip logic for this value or value is empty (unchecked), remove questions that were hidden by this question
        const questionsToRemove = new Set<string>();

        // Find all questions that were hidden by this specific question
        for (const category of categories) {
            for (const question of category.items) {
                if (!question.isContainer && question.skipLogic) {
                    // Check if this question's skip logic affects any currently hidden questions
                    for (const [skipValue, skipConfig] of Object.entries(question.skipLogic)) {
                        if (skipConfig.targetQuestionCode) {
                            let shouldHide = false;
                            for (const cat of categories) {
                                for (const q of cat.items) {
                                    if (!q.isContainer) {
                                        if (q.questionCode === question.questionCode) {
                                            shouldHide = true;
                                        } else if (q.questionCode === skipConfig.targetQuestionCode) {
                                            shouldHide = false;
                                            break;
                                        } else if (shouldHide) {
                                            questionsToRemove.add(q.questionCode);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Remove questions that were hidden by this question
        questionsToRemove.forEach(code => newHiddenQuestions.delete(code));

        // If the value is empty (unchecked), remove the skip logic answer
        if (!selectedValue) {
            delete newSkipLogicAnswers[questionCode];
        } else {
            newSkipLogicAnswers[questionCode] = selectedValue;
        }
    }

    return {
        hiddenQuestions: newHiddenQuestions,
        skipLogicAnswers: newSkipLogicAnswers
    };
};

/**
 * Checks if a question has skip logic for a specific value
 * @param question - The question to check
 * @param value - The value to check for skip logic
 * @returns True if the question has skip logic for the given value
 */
export const hasSkipLogic = (
    question: FormLayoutComponentChildrenType,
    value: string
): boolean => {
    return question.skipLogic && question.skipLogic[value];
};

/**
 * Gets all available questions that can be skipped to
 * @param categories - All form categories
 * @param currentQuestionId - ID of the current question (to exclude it)
 * @returns Array of questions that can be skipped to
 */
export const getAvailableQuestions = (
    categories: FormLayoutComponentsType[],
    currentQuestionId: string
): FormLayoutComponentChildrenType[] => {
    const questions: FormLayoutComponentChildrenType[] = [];

    const processItems = (items: any[]) => {
        items.forEach(itemObj => {
            if (!itemObj.isContainer) {
                const question = itemObj as FormLayoutComponentChildrenType;
                // Don't include the current question or questions without questionCode
                if (question.id !== currentQuestionId && question.questionCode) {
                    questions.push(question);
                }
            } else {
                // Recursively process nested containers
                if (itemObj.items) {
                    processItems(itemObj.items);
                }
            }
        });
    };

    categories.forEach(layout => {
        if (layout.items) {
            processItems(layout.items);
        }
    });

    return questions;
}; 