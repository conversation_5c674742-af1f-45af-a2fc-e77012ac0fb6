import { ReactNode } from 'react';

export interface TemplateType {
  id: string;
  name: string;
  formLayoutComponents: FormLayoutComponentsType[];
  createdAt: number;
  creator: string;
  lastPublishedAt: number;
  publishHistory: {
    lastPublishedAt: number;
    formLayoutComponents: FormLayoutComponentsType[];
  }[];
  publishStatus: string;
  updatedAt: number;
}

export interface FormLayoutComponentsType {
  container: FormLayoutComponentContainerType;
  items: FormLayoutItemType[]; // Unified array of both controls and containers
}

// Union type for all items that can be in a container
export type FormLayoutItemType = FormLayoutComponentChildrenType | FormLayoutContainerType;

export interface FormLayoutComponentContainerType {
  id: string;
  controlName: string;
  displayText: string;
  itemType: string;
  heading: string;
  subHeading: string;
  icon?: ReactNode;
}

// Generic container type that can be used for both main containers and nested subcontainers
export interface FormLayoutContainerType {
  id: string;
  controlName: string;
  displayText: string;
  itemType: string;
  heading: string;
  subHeading: string;
  containerId?: string; // Parent container ID (not present for top-level containers)
  isContainer: true;  // Flag to identify containers
  level: number;      // Nesting level (0 for main, 1+ for subcontainers)
  items: FormLayoutItemType[]; // Can contain both controls and nested containers
  icon?: ReactNode;
}

export interface FormLayoutComponentChildrenType {
  id: string;
  questionCode?: string;
  controlName: string;
  displayText: string;
  description?: string;
  labelName: string;
  placeholder?: string;
  itemType: string;
  dataType?: string;
  answer_code?: string;
  answer_text?: string[];
  index?: number;
  containerId: string;
  isContainer?: false;
  level?: number;
  icon?: ReactNode;
  isDisplayCheatSheet?: boolean;
  calculationType?: 'sum' | 'average';
  displayFormat?: string;
  selectedQuestions?: string[];
  questionScores?: {
    [questionId: string]: {
      [optionValue: string]: number;
    };
  };
  // New properties for storing calculated scores
  calculatedScore?: number; // The computed score value
  calculatedQuestionCodes?: string[]; // Array of question codes used in calculation
  lastCalculatedAt?: string; // Timestamp when score was last calculated
  // Skip logic properties
  skipLogic?: {
    [optionValue: string]: {
      targetQuestionId: string;
      targetQuestionCode?: string;
      targetQuestionLabel?: string;
    };
  };
  items?: {
    id: string;
    value: string;
    label: string;
  }[];
}
export interface FormLayoutCoponentChildrenItemsType {
  id: string;
  value: string;
  label: string;
}

export interface SelectedScoreCalcualtorItemsType {
  id: string;
  questionCode: string;
  labelName: string;
}

export interface FormLayoutCoponentChildrenItemsType {
  id: string;
  value: string;
  label: string;
}