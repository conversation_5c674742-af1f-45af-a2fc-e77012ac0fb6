import React, { FC, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import RenderItem from './RenderItem';
import RecursiveItemRenderer from './RecursiveItemRenderer';
import { FormLayoutComponentsType, FormLayoutComponentChildrenType } from '../types/FormTemplateTypes';
import { applySkipLogic } from '../utils/SkipLogicUtils';
import { FormControlNames } from '../utils/FormBuilderUtils';

interface StepperFormPreviewProps {
  formLayoutComponents: FormLayoutComponentsType[];
  screenType: string; // e.g. "mobile" or "web"
}

const StepperFormPreview: FC<StepperFormPreviewProps> = ({
  formLayoutComponents,
  screenType,
}) => {
  const [componentIndex, setComponentIndex] = useState(0);
  const component = formLayoutComponents[componentIndex];

  // Preview state - separate from original formLayoutComponents
  const [previewFormData, setPreviewFormData] = useState<FormLayoutComponentsType[]>([]);
  const [greyedOutQuestions, setGreyedOutQuestions] = useState(new Set());
  const [skipLogicAnswers, setSkipLogicAnswers] = useState({});

  // Initialize preview data when formLayoutComponents changes
  React.useEffect(() => {
    if (formLayoutComponents.length > 0) {
      setPreviewFormData(JSON.parse(JSON.stringify(formLayoutComponents)));
    }
  }, [formLayoutComponents]);

  // Simulated handleItemChange for preview - doesn't update original state
  const handlePreviewItemChange = (updatedItem: FormLayoutComponentChildrenType) => {
    const newPreviewData = [...previewFormData];

    // Find the category and item to update
    for (let i = 0; i < newPreviewData.length; i++) {
      const category = newPreviewData[i];

      // Find the item in this category
      const itemIndex = category.items.findIndex(item => item.id === updatedItem.id);

      if (itemIndex !== -1) {
        // Update the item in preview data only
        category.items[itemIndex] = updatedItem;

        // Apply skip logic if the question has skip logic configuration
        if (updatedItem.skipLogic) {
          // Determine the selected value based on the answer
          let selectedValue = "";
          if (updatedItem.answer_text && updatedItem.answer_text.length > 0) {
            // For checklist questions, we need to map the answer to the option value
            if (updatedItem.controlName === FormControlNames.CHECKLIST && updatedItem.items) {
              // Find the option that matches the answer
              const selectedOption = updatedItem.items.find(option =>
                option.label === updatedItem.answer_text![0]
              );
              selectedValue = selectedOption ? selectedOption.value : "";
            } else if (updatedItem.controlName === FormControlNames.RADIOGROUP && updatedItem.items) {
              // Find the option that matches the answer
              const selectedOption = updatedItem.items.find(option =>
                option.label === updatedItem.answer_text![0]
              );
              selectedValue = selectedOption ? selectedOption.value : "";
            } else {
              // For other question types, use the answer directly
              selectedValue = updatedItem.answer_text![0];
            }
          }

          // Apply skip logic
          const currentState = {
            hiddenQuestions: greyedOutQuestions,
            skipLogicAnswers
          };

          const newState = applySkipLogic(updatedItem.questionCode!, selectedValue, newPreviewData, currentState);
          setGreyedOutQuestions(newState.hiddenQuestions);
          setSkipLogicAnswers(newState.skipLogicAnswers);
        }

        break;
      }
    }

    setPreviewFormData(newPreviewData);
  };

  const nextPrevIndex = (val: number) => {
    setComponentIndex(prev => prev + val);
  };
  // console.log(JSON.stringify(formLayoutComponents))
  if (formLayoutComponents.length === 0) {
    return (
      <View style={styles.noContentContainer}>
        <Text style={styles.noContentText}>
          You need to add Containers and Controls to see output here.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.root}>
      <View style={styles.previewWindow}>
        {/* Heading & Subheading */}


        {/* Scrollable content area within the mobile preview */}
        <ScrollView style={styles.scrollArea} contentContainerStyle={styles.scrollContent}>
          {/* Render each item, handling nested subcontainers recursively */}

          {previewFormData.map((comp) => {
            return (
              <View key={comp.container.id}>
                {/* <View style={styles.stepTitleContainer}>
                  <Text style={styles.stepTitle}> {comp.container.heading}</Text>
                </View> */}


                <View style={styles.headingContainer}>
                  <Text style={[
                    styles.headingText,
                    // Check if any question in this component is greyed out
                    comp.items.some(item => 'questionCode' in item && item.questionCode && greyedOutQuestions.has(item.questionCode)) ? styles.greyedOutText : null
                  ]}>
                    {comp.container.heading} : {comp.container.subHeading}
                  </Text>
                </View>
                {comp.items.map((item) => {
                  const isGreyedOut = 'questionCode' in item && item.questionCode ? greyedOutQuestions.has(item.questionCode) : false;
                  return (
                    <RecursiveItemRenderer
                      key={item.id}
                      item={item}
                      level={item.level || 0}
                      onItemChange={handlePreviewItemChange}
                      isGreyedOut={isGreyedOut}
                    />
                  );
                })}
              </View>
            )
          })}

        </ScrollView>

        {/* Navigation Buttons - outside of ScrollView */}
        {/* <View style={styles.buttonRow}>
          {componentIndex !== 0 && (
            <TouchableOpacity
              style={[styles.navButton, styles.backButton]}
              onPress={() => nextPrevIndex(-1)}
            >
              <Text style={styles.navButtonText}>Back</Text>
            </TouchableOpacity>
          )}

          {componentIndex < formLayoutComponents.length - 1 && (
            <TouchableOpacity
              style={[styles.navButton, styles.nextButton]}
              onPress={() => nextPrevIndex(1)}
            >
              <Text style={styles.navButtonText}>Next</Text>
            </TouchableOpacity>
          )}

          {componentIndex + 1 === formLayoutComponents.length && (
            <TouchableOpacity
              style={styles.submitButton}
              onPress={() => {
                console.log('Submit clicked');
              }}
            >
              <Text style={styles.submitButtonText}>Submit</Text>
            </TouchableOpacity>
          )}
        </View> */}
      </View>
    </View>
  );
};

export default StepperFormPreview;

// -------------- Styles --------------
const styles = StyleSheet.create({
  root: {
    width: 354,
    height: 765,
  },
  scrollArea: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  noContentContainer: {
    flex: 1,
    alignItems: 'center',
  },
  noContentText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: '#1F2937',
    textAlign: 'center',
  },
  previewWindow: {
    backgroundColor: '#FFFFFF',
    width: 354,
    height: 765,
    padding: 16,
    paddingBottom: 0, // Reduce bottom padding to make space for buttons
    borderRadius: 44,
    flexDirection: 'column',
    flex: 1,
    overflow: 'hidden', // Ensure nothing spills outside the container
  },
  stepTitleContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    paddingBottom: 10,
  },
  stepTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#1F2937',
    textAlign: 'center',
  },
  headingContainer: {
    marginVertical: 16,
    // alignItems: 'center',
  },
  headingText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center', // Center buttons
    alignItems: 'center',
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  navButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginHorizontal: 8, // Adds space between buttons
  },
  backButton: {
    backgroundColor: '#F3F4F6',
  },
  nextButton: {
    backgroundColor: '#F3F4F6',
  },
  navButtonText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#1F2937',
  },
  submitButton: {
    backgroundColor: '#4C51BF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  submitButtonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 14,
    color: '#FFFFFF',
  },
  greyedOutText: {
    color: '#9CA3AF',
    opacity: 0.6,
  },
});