import React, { FC, useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { FormLayoutComponentChildrenType } from 'src/features/form-builder/types/FormTemplateTypes';

interface ScoreCalculatorDisplayProps {
  item: FormLayoutComponentChildrenType;
  allAnswers: Record<string, string[]>;
  containerItems: FormLayoutComponentChildrenType[];
  onChange?: (updatedItem: FormLayoutComponentChildrenType) => void;
}

const ScoreCalculatorDisplay: FC<ScoreCalculatorDisplayProps> = ({
  item,
  allAnswers,
  containerItems,
  onChange
}) => {
  const [calculatedScore, setCalculatedScore] = useState<number>(0);
  const [questionCodes, setQuestionCodes] = useState<string[]>([]);
  
  // Use refs to track state and prevent unnecessary calls
  const isCalculatingRef = useRef(false);
  const lastCalculationRef = useRef<string>('');
  const lastSavedScoreRef = useRef<number | undefined>(undefined);
  const lastSavedCodesRef = useRef<string[] | undefined>(undefined);

  // Calculate score whenever answers change
  useEffect(() => {
    // Create a unique key for this calculation to prevent unnecessary recalculations
    const calculationKey = JSON.stringify({
      allAnswers,
      questionScores: item.questionScores,
      calculationType: item.calculationType
    });

    // Only calculate if something actually changed
    if (calculationKey !== lastCalculationRef.current && !isCalculatingRef.current) {
      lastCalculationRef.current = calculationKey;
      calculateScore();
    }
  }, [allAnswers, item.questionScores, item.calculationType]);

  // Separate useEffect for syncing with external updates to avoid conflicts
  useEffect(() => {
    // Only update local state if we're not currently calculating
    // and the values are different from what we calculated
    if (!isCalculatingRef.current) {
      if (item.calculatedScore !== undefined && item.calculatedScore !== calculatedScore) {
        setCalculatedScore(item.calculatedScore);
      }
      if (item.calculatedQuestionCodes && 
          JSON.stringify(item.calculatedQuestionCodes) !== JSON.stringify(questionCodes)) {
        setQuestionCodes(item.calculatedQuestionCodes);
      }
    }
  }, [item.calculatedScore, item.calculatedQuestionCodes]);

  const calculateScore = () => {
    if (!item.questionScores || !item.calculationType) {
      updateScoreState(0, []);
      return;
    }

    isCalculatingRef.current = true;

    let totalScore = 0;
    let validScores = 0;
    const codes: string[] = [];

    // Iterate through each question mapped in questionScores
    Object.keys(item.questionScores).forEach(questionId => {
      const questionScoreMapping = item.questionScores[questionId];
      const questionAnswers = allAnswers[questionId];
      
      // Find the question item to get its label
      const questionItem = containerItems.find(containerItem => containerItem.id === questionId);
      const questionCode = questionItem?.questionCode;

      if (questionAnswers && questionAnswers.length > 0) {
        // Handle different control types
        const selectedAnswer = questionAnswers[0];
        
        if (selectedAnswer && selectedAnswer !== 'Not Available' && selectedAnswer !== '') {
          // For radio buttons and dropdowns, we need to find the value from the selected label
          let scoreValue = 0;

          if (questionItem?.controlName === 'radio-group' || questionItem?.controlName === 'select-drop-down') {
            // Find the corresponding value for the selected label
            const selectedOption = questionItem.items?.find(option => option.label === selectedAnswer);
            if (selectedOption && questionScoreMapping[selectedOption.value] !== undefined) {
              scoreValue = questionScoreMapping[selectedOption.value];
            }
          } else if (questionItem?.controlName === 'checklist') {
            // For checklist, sum up scores for all selected items
            questionAnswers.forEach(answer => {
              if (answer && answer !== 'Not Available') {
                const selectedOption = questionItem.items?.find(option => option.label === answer);
                if (selectedOption && questionScoreMapping[selectedOption.value] !== undefined) {
                  scoreValue += questionScoreMapping[selectedOption.value];
                }
              }
            });
          } else {
            // For other control types, try direct mapping
            if (questionScoreMapping[selectedAnswer] !== undefined) {
              scoreValue = questionScoreMapping[selectedAnswer];
            }
          }

          if (scoreValue > 0 || questionScoreMapping[selectedAnswer] === 0) {
            totalScore += scoreValue;
            validScores++;
            
            // Add question code to array if it exists and score is valid
            if (questionCode && !codes.includes(questionCode)) {
              codes.push(questionCode);
            }
          }
        }
      }
    });

    // Calculate final score based on calculation type
    let finalScore = 0;
    if (item.calculationType === 'sum') {
      finalScore = totalScore;
    } else if (item.calculationType === 'average' && validScores > 0) {
      finalScore = totalScore / validScores;
    }

    updateScoreState(finalScore, codes);
  };

  const updateScoreState = (score: number, codes: string[]) => {
    // Check if values actually changed before updating
    const scoreChanged = calculatedScore !== score;
    const codesChanged = JSON.stringify(questionCodes) !== JSON.stringify(codes);
    
    if (scoreChanged || codesChanged) {
      // Update local state in a single batch
      setCalculatedScore(score);
      setQuestionCodes(codes);
      
      // Only save if values actually changed and we haven't saved these exact values before
      const shouldSave = (
        lastSavedScoreRef.current !== score || 
        JSON.stringify(lastSavedCodesRef.current) !== JSON.stringify(codes)
      );
      
      if (shouldSave) {
        // Update our refs to track what we're saving
        lastSavedScoreRef.current = score;
        lastSavedCodesRef.current = [...codes];
        
        // Save to parent component
        saveCalculatedScore(score, codes);
      }
    }
    
    // Reset the calculating flag
    isCalculatingRef.current = false;
  };

  const formatScore = (score: number): string => {
    if (item.calculationType === 'average') {
      let format=item.displayFormat;
      if(format ===""){
        return score.toFixed(0);
      }else{
        if(format?.toLowerCase()==".x" || format?.toLowerCase()==".xx"){
        return score.toFixed(format.length-1);
        }
      }
      
    }
    return score.toString();
  };

  const saveCalculatedScore = (score: number, codes: string[]) => {
    
    
    if (onChange) {
      const updatedItem: FormLayoutComponentChildrenType = {
        ...item,
        calculatedScore: score,
        calculatedQuestionCodes: codes,
        lastCalculatedAt: new Date().toISOString(),
        // Also save as answer_text for consistency with other form fields
        answer_text: [formatScore(score)]
      };
      
      onChange(updatedItem);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.leftSection}>
        <Image source={require('assets/images/score.png')} style={styles.icon} />
        <View style={styles.textContainer}>
          <Text style={styles.scoreTitle}>Score</Text>
          {questionCodes.length > 0 && (
            <Text style={styles.questionCodes}>
              {questionCodes.join(', ')}
            </Text>
          )}
        </View>
      </View>
      <Text style={styles.scoreValue}>{formatScore(calculatedScore)}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  textContainer: {
    flex: 1,
  },
  scoreTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    color: '#374151',
  },
  questionCodes: {
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  scoreValue: {
    fontSize: 24,
    fontFamily: 'Poppins_700Bold',
    color: '#3B82F6',
  },
});

export default ScoreCalculatorDisplay;