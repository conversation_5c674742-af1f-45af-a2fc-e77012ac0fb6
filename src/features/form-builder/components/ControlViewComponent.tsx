import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Switch,
  Modal,
  FlatList,
  Platform,
} from 'react-native';
import { useDrag, useDrop } from 'react-dnd';
// import { Trash2, GripVertical, ChevronDown, ArrowUp, ArrowDown, InfoIcon } from 'lucide-react-native';
import Checkbox from 'expo-checkbox';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { FormLayoutComponentChildrenType, SelectedScoreCalcualtorItemsType } from '../types/FormTemplateTypes';
import { FormItemTypes, FormControlNames } from '../utils/FormBuilderUtils';
const isWeb = Platform.OS === 'web';
import RenderItem from './RenderItem';
import { Hoverable } from 'react-native-web-hover';
import { AntDesign, Feather } from '@expo/vector-icons';

interface ControlViewComponentProps {
  item: FormLayoutComponentChildrenType;
  deleteControl: (itemId: string, containerId: string, subcontainerId?: string) => void;
  containerId: string;
  subcontainerId?: string;
  selectControl: (item: FormLayoutComponentChildrenType) => void;
  selectedControl: FormLayoutComponentChildrenType | null;
  index: number;
  itemsLength: number;
  moveControl: (
    item: FormLayoutComponentChildrenType,
    dragIndex: number,
    hoverIndex: number,
    containerId: string,
    subcontainerId?: string,
  ) => void;
  selectedScoreItems?:SelectedScoreCalcualtorItemsType[]
}

const ControlViewComponent: React.FC<ControlViewComponentProps> = ({
  item,
  deleteControl,
  containerId,
  subcontainerId,
  selectControl,
  selectedControl,
  index,
  itemsLength,
  moveControl,
  selectedScoreItems,
}) => {
  const ref = useRef<View>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  // Add subcontainerId to the item for dragging
  const dragItem = { ...item, index, subcontainerId };

  // Dragging logic
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: FormItemTypes.CONTROL,
      item: dragItem,
      collect: monitor => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [dragItem, index],
  );

  // Dropping logic
  const [{ isOver }, drop] = useDrop(
    () => ({
      accept: FormItemTypes.CONTROL,
      hover: (draggedItem: FormLayoutComponentChildrenType) => {
        /**
         * open this code for reordering component
        */

        // if (draggedItem.index !== index) {
        //   moveControl(
        //     draggedItem,
        //     draggedItem.index,
        //     index,
        //     containerId,
        //     subcontainerId
        //   );
        //   draggedItem.index = index;
        // }
      },
      collect: monitor => ({
        isOver: monitor.isOver(),
      }),
    }),
    [index, moveControl, containerId, subcontainerId],
  );

  drag(drop(ref)); // Attach drag & drop to component

  const opacity = isDragging ? 0.5 : 1;
  const isSelected = selectedControl && selectedControl.id === item.id;
  const movePosition = (targetPosition: number) => {

    moveControl(
      item,
      index,
      targetPosition,
      containerId
    );
  }
  return (
    <View
      ref={ref}
      style={[styles.container, isSelected && styles.selected, { opacity }]}
    >
      <TouchableOpacity
        onPress={() => selectControl(item)}
        style={styles.content}
      >
        <View style={styles.header}>
          <Text style={styles.label}>
            {`${item.questionCode || index + 1}. ${item.labelName}`}
            <Text style={styles.required}>{item.required ? ' *' : ''}</Text>
          </Text>
          <View style={styles.actions}>
            {index > 0 &&
              <TouchableOpacity style={styles.iconRound} onPress={() => movePosition(index - 1)}>
                <AntDesign name="arrowup" size={16} color="#4B5563" />
                {/* <ArrowUp size={16} color="#4B5563" /> */}
              </TouchableOpacity>
            }
            {index < itemsLength - 1 &&
              <TouchableOpacity style={styles.iconRound} onPress={() => movePosition(index + 1)}>
                <AntDesign name="arrowdown" size={16} color="#4B5563" />
                {/* <ArrowDown size={16} color="#4B5563" /> */}
              </TouchableOpacity>
            }
            <TouchableOpacity
              onPress={() => deleteControl(item.id, containerId, subcontainerId)}
              style={styles.icon}
            >
              <Feather name="trash-2" size={20} color="#E53E3E" />
              {/* <Trash2 size={20} color="#E53E3E" /> */}
            </TouchableOpacity>
            <Hoverable>
              {({ hovered }) => (
                <View >
                  <Feather name="info" size={20} color="#4B5563" />
                  {/* <InfoIcon size={20} color="#4B5563" /> */}
                  {hovered && (
                    <View style={styles.tooltip}>
                      <Text style={styles.tooltipText}>{item.isDisplayCheatSheet?"Displaying on Cheatsheet" :"Hidden from Cheatsheet"}</Text>
                    </View>
                  )}
                </View>
              )}
            </Hoverable>
            
          </View>
        </View>
        {item.description ? (
          <Text style={styles.description}>{item.description}</Text>
        ) : null}
        <View style={styles.previewBox}>
          <RenderItem item={item} selectedScoreItems={selectedScoreItems}/>
        </View>
      </TouchableOpacity>
    </View>
  );
};



export default ControlViewComponent;

const styles = StyleSheet.create({
  tooltip: {
    position: 'absolute',
    top: -35,
    right: 0,
    backgroundColor: '#333',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 4,
    zIndex: 100,
    minWidth:160,
    minHeight:30
  },
  tooltipText: {
    color: '#fff',
    fontSize: 14,
  },
  container: {
    width: '100%',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#E5E7EB',
  },
  selected: {
    borderColor: 'rgb(255, 193, 7)',
    borderWidth: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    color: '#1F2937',
    fontFamily: 'Poppins_600SemiBold',
  },
  required: {
    color: '#E53E3E',
  },
  description: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 8,
    fontFamily: 'Poppins_400Regular',
  },
  previewBox: {
    marginTop: 8,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    padding: 6,
    marginLeft: 8,
  },
  iconRound: {
    padding: 6,
    marginLeft: 8,
    backgroundColor: "#ccc9c9",
    borderRadius: 18,

  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#000',
    marginBottom: 8,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkbox: {
    marginRight: 8,
    borderRadius: 4,
  },
  labelText: {
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
  },
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#4C51BF',
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioOuterSelected: {
    borderColor: '#4C51BF',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4C51BF',
  },
  selectBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    padding: 10,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  selectBoxText: {
    color: '#1F2937',
    fontFamily: 'Poppins_400Regular',
  },
  placeholderBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    alignItems: 'center',
  },
  uploadBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  signatureBox: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#4C51BF',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    marginBottom: 8,
  },
  signatureText: {
    color: '#4C51BF',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
  },
  dropdownItem: {
    padding: 12,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
    textAlign: 'center',
  },
});