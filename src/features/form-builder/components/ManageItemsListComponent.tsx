import { Feather } from '@expo/vector-icons';
import React, { FC, useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
// import { Pencil, Trash2 } from 'lucide-react-native';
import { FormLayoutCoponentChildrenItemsType } from 'src/features/form-builder/types/FormTemplateTypes';
import { generateID } from 'src/utils/GeneralUtils';

// The props from your MUI-based version, now in RN style
interface ManageItemsListComponentProps {
  items: FormLayoutCoponentChildrenItemsType[] | undefined;
  addItemInList: (item: FormLayoutCoponentChildrenItemsType) => void;
  deleteItemFromList: (item: FormLayoutCoponentChildrenItemsType) => void;
  editIteminList: (item: FormLayoutCoponentChildrenItemsType) => void;
}

const ManageItemsListComponent: FC<ManageItemsListComponentProps> = props => {
  const { items, addItemInList, deleteItemFromList, editIteminList } = props;

  // itemName is what user types in the text input
  const [itemName, setItemName] = useState<string>('');

  const [itemCode, setItemCode] = useState<string>('');

  // whether we’re editing or adding
  const [editMode, setEditMode] = useState<boolean>(false);
  // the item ID we’re editing
  const [editItemId, setEditItemId] = useState<string | undefined>(undefined);

  // If the parent changes items, we reset local states
  useEffect(() => {
    cancelEditing();
  }, [items]);

  const handleChangeText = (text: string) => {
    setItemName(text);
  };

  const handleChangeCode = (text: string) => {
    setItemCode(text);
  };

  // Switch to edit mode for a given item
  const changeToEditMode = (item: FormLayoutCoponentChildrenItemsType) => {
    setItemName(item.label);
    setEditItemId(item.id);
    setItemCode(item.value);
    setEditMode(true);
  };

  // "Submit" logic, for both add or edit
  const onSubmit = () => {
    if (itemName.trim() !== '') {
      if (!editMode) {
        // Add new item
        addItemInList({
          id: generateID(), // or your own ID logic
          value: `${(items?.length || 0) + 1}`,
          label: itemName,
        });
      } else {
        // Edit existing item
        editIteminList({
          id: editItemId as string,
          value: itemCode,
          label: itemName,
        });
      }
      cancelEditing();
    }
  };

  const cancelEditing = () => {
    setEditMode(false);
    setItemName('');
    setItemCode('');
    setEditItemId(undefined);
  };

  return (
    <View style={styles.container}>
      {/* TextInput for item name */}
      <Text style={styles.label}>Option Name</Text>
      <TextInput
        style={styles.textInput}
        value={itemName}
        onChangeText={handleChangeText}
      />

      <Text style={styles.label}>Option code</Text>
      <TextInput
        style={styles.textInput}
        value={itemCode}
        onChangeText={handleChangeCode}
      />

      {/* Add / Edit button */}
      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.addEditButton} onPress={onSubmit}>
          <Text style={styles.addEditButtonText}>
            {editMode ? 'Edit Option' : 'Add Option'}
          </Text>
        </TouchableOpacity>
        {editMode && (
          <TouchableOpacity style={styles.cancelButton} onPress={cancelEditing}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Items list */}
      <ScrollView style={styles.itemList}>
        {(items || []).map(item => (
          <View key={item.id} style={styles.itemRow}>
            <Text style={styles.itemLabel}>{item.label}</Text>
            <View style={styles.iconRow}>
              {/* Edit button */}
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => changeToEditMode(item)}
              >
                <Feather name="edit" size={18} color="#4B5563" />
                {/* <Pencil color="#4B5563" size={18} /> */}
              </TouchableOpacity>

              {/* Delete button */}
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => deleteItemFromList(item)}
              >
                <Feather name="trash-2" size={18} color="#EF4444" />
                {/* <Trash2 color="#EF4444" size={18} /> */}
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default ManageItemsListComponent;

// -------------- Styles --------------
const styles = StyleSheet.create({
  container: {
    marginTop: 10,
  },
  label: {
    fontSize: 14,
    marginBottom: 4,
    color: '#1F2937',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    padding: 10,
    marginBottom: 10,
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#000',
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginBottom: 16,
  },
  addEditButton: {
    backgroundColor: '#4C51BF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  addEditButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  cancelButton: {
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: '#1F2937',
    fontSize: 14,
  },
  itemList: {
    maxHeight: 200, // or adapt to your layout
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: 8,
  },
  itemRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemLabel: {
    fontSize: 14,
    color: '#1F2937',
    fontFamily: 'Poppins_400Regular',
  },
  iconRow: {
    flexDirection: 'row',
    gap: 10,
  },
  iconButton: {
    padding: 4,
  },
});
