import React, { FC, useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Platform,
  Alert,
  Dimensions,
  Modal,
} from 'react-native';
import Checkbox from 'expo-checkbox';
import { FormControlNames } from 'src/features/form-builder/utils/FormBuilderUtils';
import { FormLayoutComponentChildrenType, SelectedScoreCalcualtorItemsType } from 'src/features/form-builder/types/FormTemplateTypes';
import { Ionicons } from '@expo/vector-icons';
import ScoreCalculatorDisplay from './ScoreCalculatorDisplay';
import DateTimePicker from '@react-native-community/datetimepicker';
import theme from '@/src/theme';

const isWeb = Platform.OS === 'web';
const THEME_COLOR = '#007AFF';
const WARNING_COLOR = '#F6A70A'; // Orange color for highlighting unanswered fields

interface RenderItemProps {
  item: FormLayoutComponentChildrenType;
  onChange?: (updatedItem: FormLayoutComponentChildrenType) => void;
  onAnswerUpdate?: () => void; // Callback to notify AllQuestion component
  allAnswers?: Record<string, string[]>;
  containerItems?: FormLayoutComponentChildrenType[];
  selectedScoreItems?: SelectedScoreCalcualtorItemsType[];
  disabled?: boolean;
  isDisabledFields?: boolean; // New prop to disable fields

}

const isItemAnswered = (item: FormLayoutComponentChildrenType) => {
  if (!item || !item.answer_text || !Array.isArray(item.answer_text)) {
    return false;
  }

  // Check if answer_text is empty or contains 'Not Available'
  if (item.answer_text.length === 0 ||
    (item.answer_text.length === 1 &&
      (item.answer_text[0] === 'Not Available' || item.answer_text[0] === ''))) {

    // Special check for checkboxes, toggles, and radio buttons where "No" is a valid answer
    if (item.controlName === FormControlNames.CHECKBOX ||
      item.controlName === FormControlNames.TOGGLE ||
      item.controlName === FormControlNames.RADIOGROUP) {
      // For these controls, "No" is a valid answer, but a null or empty answer is not
      return item.answer_text[0] !== '' && item.answer_text[0] !== 'Not Available';
    }

    return false;
  }

  // Special handling for date fields - check if the date is valid
  if (item.controlName === FormControlNames.DATEFIELD) {
    const dateValue = item.answer_text[0];
    if (!dateValue || dateValue === 'Not Available' || dateValue === '') {
      return false;
    }
    // Check if it's a valid date
    const parsedDate = new Date(dateValue);
    return !isNaN(parsedDate.getTime());
  }

  return true;
};

const ErrorDisplay = ({ message }: { message: string | null }) => {
  if (!message) return null;

  return (
    <View style={styles.errorMessageContainer}>
      <Text style={styles.errorText}>{message}</Text>
    </View>
  );
};

const NotAnsweredWarning = () => {
  return (
    <View style={styles.warningContainer}>
      <Text style={styles.warningText}>Not Answered</Text>
    </View>
  );
};

const formatDateMMDDYYYY = (date: Date | null | undefined | string) => {
  try {
    if (!date || date === "" || date === "Not Available") {
      return '';
    }

    // If it's a string, try to parse it first
    let dateObj: Date;
    if (typeof date === 'string') {
      dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        return '';
      }
    } else {
      dateObj = date;
    }

    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${month}/${day}/${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

const RenderItem: FC<RenderItemProps> = ({ item, onChange, onAnswerUpdate, allAnswers = {},
  containerItems = [], selectedScoreItems = [], disabled = false, isDisabledFields = false }) => {

  const [inputError, setInputError] = useState<string | null>(null);
  const [isAnswered, setIsAnswered] = useState<boolean>(isItemAnswered(item));
  const [isPickerVisible, setPickerVisible] = useState(false);

  // Safe getter for item properties with validation
  const safeGetItem = () => {
    if (!item) {
      const error = 'Item is undefined or null';
      handleError(error);
      return null;
    }

    // Ensure controlName exists, defaulting to empty string if missing
    if (!item.controlName) {
      const tempItem = { ...item };
      tempItem.controlName = '';
      console.warn('Item is missing controlName property');
      return tempItem;
    }

    return item;
  };

  // Error handling function
  const handleError = (errorMessage: string) => {
    setInputError(errorMessage);
    if (!isWeb) {
      // Show Alert only on mobile platforms
      Alert.alert('Form Error', errorMessage);
    } else {
      console.error(errorMessage);
    }
  };

  // Helper function to get the first answer value or empty string with error handling
  // const getFirstAnswer = (): string => {
  //   try {
  //     const safeItem = safeGetItem();
  //     if (!safeItem) return '';

  //     if (safeItem.answer_text &&
  //       Array.isArray(safeItem.answer_text) &&
  //       safeItem.answer_text.length > 0 &&
  //       safeItem.answer_text[0] !== 'Not Available') {
  //       return safeItem.answer_text[0];
  //     }
  //     return '';
  //   } catch (error) {
  //     handleError(`Error retrieving answer: ${error instanceof Error ? error.message : String(error)}`);
  //     return '';
  //   }
  // };

  const getFirstAnswer = (): string => {
    try {
      const safeItem = safeGetItem();
      if (!safeItem) return '';

      if (safeItem.answer_text &&
        Array.isArray(safeItem.answer_text) &&
        safeItem.answer_text.length > 0 &&
        safeItem.answer_text[0] !== 'Not Available' &&
        safeItem.answer_text[0] !== '') {
        return safeItem.answer_text[0];
      }
      return '';
    } catch (error) {
      handleError(`Error retrieving answer: ${error instanceof Error ? error.message : String(error)}`);
      return '';
    }
  };

  // Get control type once for all state initializations
  const controlType = safeGetItem()?.controlName || '';

  // Initialize states with values from answer_text when available
  const [textValue, setTextValue] = useState(() => {
    try {
      if (controlType === FormControlNames.INPUTTEXTFIELD ||
        controlType === FormControlNames.INPUTMULTILINE) {
        return getFirstAnswer();
      }
      return '';
    } catch (error) {
      handleError(`Error initializing text value: ${error instanceof Error ? error.message : String(error)}`);
      return '';
    }
  });
  // State for toggle/switch
  const [switchValue, setSwitchValue] = useState(() => {
    try {
      if (controlType === FormControlNames.TOGGLE) {
        const answer = getFirstAnswer();
        return answer === 'Yes' || answer === 'true';
      }
      return false;
    } catch (error) {
      handleError(`Error initializing switch value: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  });

  // State for checkbox
  const [checkValue, setCheckValue] = useState(() => {
    try {
      if (controlType === FormControlNames.CHECKBOX) {
        const answer = getFirstAnswer();
        return answer === 'Yes' || answer === 'true';
      }
      return false;
    } catch (error) {
      handleError(`Error initializing checkbox value: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  });

  const [selectedRadio, setSelectedRadio] = useState<string | null>(() => {
    try {
      if (controlType === FormControlNames.RADIOGROUP) {
        const answer = getFirstAnswer();
        const safeItem = safeGetItem();
        if (!safeItem || !answer || !safeItem.items) return null;

        // Strategy 1: Direct label match (most reliable)
        const directMatch = safeItem.items.find(i => i && i.label === answer);
        if (directMatch) {
          return directMatch.value;
        }

        // Strategy 2: Try to find by extracting pattern from answer and matching with label patterns
        const answerMatch = answer.match(/^(\d+|[A-Z]+)\s*[\.\-]/);
        if (answerMatch) {
          const extractedFromAnswer = answerMatch[1];

          // Find item where label starts with the same pattern
          const patternMatch = safeItem.items.find(i => {
            if (!i || !i.label) return false;
            const labelMatch = i.label.match(/^(\d+|[A-Z]+)\s*[\.\-]/);
            return labelMatch && labelMatch[1] === extractedFromAnswer;
          });

          if (patternMatch) {
            return patternMatch.value;
          }
        }

        // Strategy 3: Legacy fallback - check if answer is actually a value
        const valueMatch = safeItem.items.find(i => i && i.value === answer);
        if (valueMatch) {
          return valueMatch.value;
        }
      }
      return null;
    } catch (error) {
      handleError(`Error initializing radio value: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  });

  // State for checklist
  const [checklistValues, setChecklistValues] = useState<string[]>(() => {
    try {
      if (controlType === FormControlNames.CHECKLIST) {
        const safeItem = safeGetItem();
        if (!safeItem) return [];

        if (safeItem.answer_text &&
          Array.isArray(safeItem.answer_text) &&
          safeItem.answer_text.length > 0 &&
          safeItem.answer_text[0] !== 'Not Available') {

          // For checklist, each selected item might be in the array
          const values: string[] = [];

          safeItem.answer_text.forEach(answer => {
            if (answer && safeItem.items) {


              // Extract value from displayed text (e.g. "A - White" => "A")
              const matchedItem = safeItem.items.find(i => i && i.label === answer);
              if (matchedItem) {
                values.push(matchedItem.value);
              }
              else {
                // If no pattern match, try to find the item with matching label
                const match = answer.match(/^(\d+|[A-Z])\s*-/);
                if (match) {
                  values.push(match[1]);
                }
              }
            }
          });
          return values;
        }
      }
      return [];
    } catch (error) {
      handleError(`Error initializing checklist values: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  });

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  // State for date picker
  // const [date, setDate] = useState(() => {
  //   try {
  //     if (controlType === FormControlNames.DATEFIELD) {
  //       const answer = getFirstAnswer();
  //       console.log("answer : ", answer)
  //       // Handle "Not Available" or other invalid date strings
  //       try {
  //         const dateValue = answer ? new Date(answer) : "";
  //         // Validate that the parsed date is valid
  //         if (dateValue == "") {
  //           return ""
  //         }
  //         // if (isNaN(dateValue.getTime())) {
  //         //   return new Date();
  //         // }
  //         return dateValue;
  //       } catch (e) {
  //         handleError(`Invalid date format: ${e instanceof Error ? e.message : String(e)}`);
  //         return new Date();
  //       }
  //     }
  //     return new Date();
  //   } catch (error) {
  //     handleError(`Error initializing date value: ${error instanceof Error ? error.message : String(error)}`);
  //     return new Date();
  //   }
  // });

  const [date, setDate] = useState<Date | null>(() => {
    try {
      if (controlType === FormControlNames.DATEFIELD) {
        const answer = getFirstAnswer();
        // Only try to parse if we have a valid answer (not empty and not "Not Available")
        if (answer && answer !== 'Not Available') {
          const parsed = new Date(answer);
          if (!isNaN(parsed.getTime())) {
            return parsed;
          }
        }
        // Return null if no valid date is available
        return null;
      }
    } catch (e) {
      handleError(`Date init error: ${e instanceof Error ? e.message : String(e)}`);
    }
    return null; // ✅ Return null instead of current date for no selection
  });

  const [tempDate, setTempDate] = useState<Date>(() => {
    // For tempDate, we need a valid Date object, so use current date as fallback
    return date || new Date();
  });

  // State for Numeric with Units component
  const [numericWithUnitsValue, setNumericWithUnitsValue] = useState(() => {
    try {
      if (controlType === FormControlNames.NUMERICWITHUNITS) {
        const answer = getFirstAnswer();
        if (answer) {
          try {
            // Try to parse the JSON string
            return JSON.parse(answer);
          } catch (e) {
            // If parsing fails, return default values
            handleError(`Failed to parse numeric with units value: ${e instanceof Error ? e.message : String(e)}`);
            return { value: '', unit: '' };
          }
        }
      }
      return { value: '', unit: '' };
    } catch (error) {
      handleError(`Error initializing numeric values: ${error instanceof Error ? error.message : String(error)}`);
      return { value: '', unit: '' };
    }
  });


  // Helper function to update the answer_text with error handling
  const updateAnswerText = (value: any) => {
    try {
      const safeItem = safeGetItem();
      if (!safeItem) return;

      let answerText: string[];

      // Format the value based on the control type
      if (value === null || value === undefined || value === '') {
        answerText = ['Not Available'];
        setIsAnswered(false);
      } else if (Array.isArray(value)) {
        // For checklist values
        if (value.length === 0) {
          answerText = ['Not Available'];
          setIsAnswered(false);
        } else {
          // Convert values to their label representation based on items
          answerText = value.map(val => {
            if (safeItem.items) {
              const matchedItem = safeItem.items.find(i => i && i.value === val);
              return matchedItem ? matchedItem.label : val;
            }
            return val;
          });
          setIsAnswered(true);
        }
      } else if (typeof value === 'boolean') {
        // For boolean values (checkbox, toggle)
        answerText = [value ? 'Yes' : 'No'];
        // For checkbox and toggle, "No" is still a valid answer
        setIsAnswered(true);
      } else if (safeItem.controlName === FormControlNames.RADIOGROUP && safeItem.items) {
        // For radio buttons, get the full label
        const matchedItem = safeItem.items.find(i => i && i.value === value);
        if (matchedItem) {
          answerText = [matchedItem.label];
          setIsAnswered(true);
        } else {
          // Fallback: if we can't find the item, store the value as-is
          answerText = [String(value)];
          setIsAnswered(true);
        }
      } else if (safeItem.controlName === FormControlNames.SELECTDROPDOWN && safeItem.items) {
        // For dropdowns, get the label
        const matchedItem = safeItem.items.find(i => i && i.id === value);
        answerText = matchedItem ? [matchedItem.label] : ['Not Available'];
        setIsAnswered(matchedItem ? true : false);
      } else {
        // For other types
        answerText = [String(value)];
        setIsAnswered(true);
      }

      const updatedItem = {
        ...safeItem,
        answer_text: answerText,
      };

      if (onChange) {
        onChange(updatedItem);
      }

      // Notify AllQuestion component that an answer has been updated
      if (onAnswerUpdate) {
        onAnswerUpdate();
      }

      // Clear any previous error
      setInputError(null);
    } catch (error) {
      handleError(`Error updating answer: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
  // Effect to sync state with item.answer_text if it changes externally
  useEffect(() => {
    try {
      const safeItem = safeGetItem();
      if (!safeItem) return;

      // Update isAnswered state based on the current item
      setIsAnswered(isItemAnswered(safeItem));

      const answerValue = getFirstAnswer();

      switch (safeItem.controlName) {
        case FormControlNames.INPUTTEXTFIELD:
        case FormControlNames.INPUTMULTILINE:
          setTextValue(answerValue);
          break;
        case FormControlNames.CHECKBOX:
          setCheckValue(answerValue === 'Yes' || answerValue === 'true');
          break;
        case FormControlNames.TOGGLE:
          setSwitchValue(answerValue === 'Yes' || answerValue === 'true');
          break;
        case FormControlNames.RADIOGROUP:
          if (answerValue && safeItem.items) {
            // Strategy 1: Direct label match
            const directMatch = safeItem.items.find(i => i && i.label === answerValue);
            if (directMatch) {
              setSelectedRadio(directMatch.value);
              break;
            }

            // Strategy 2: Pattern matching from stored answer
            const answerMatch = answerValue.match(/^(\d+|[A-Z]+)\s*[\.\-]/);
            if (answerMatch) {
              const extractedFromAnswer = answerMatch[1];

              // Find item where label starts with the same pattern
              const patternMatch = safeItem.items.find(i => {
                if (!i || !i.label) return false;
                const labelMatch = i.label.match(/^(\d+|[A-Z]+)\s*[\.\-]/);
                return labelMatch && labelMatch[1] === extractedFromAnswer;
              });

              if (patternMatch) {
                setSelectedRadio(patternMatch.value);
                break;
              }
            }

            // Strategy 3: Check if the answer is actually a value
            const valueMatch = safeItem.items.find(i => i && i.value === answerValue);
            if (valueMatch) {
              setSelectedRadio(valueMatch.value);
              break;
            }

            // If no match found, set to null
            setSelectedRadio(null);
          } else {
            setSelectedRadio(null);
          }
          break;
        case FormControlNames.CHECKLIST:
          if (safeItem.answer_text &&
            Array.isArray(safeItem.answer_text) &&
            safeItem.answer_text.length > 0 &&
            safeItem.answer_text[0] !== 'Not Available') {
            const values: string[] = [];
            safeItem.answer_text.forEach(answer => {
              if (answer && safeItem.items) {

                //  find item with matching label
                const matchedItem = safeItem.items.find(i => i && i.label === answer);
                if (matchedItem) {
                  values.push(matchedItem.value);
                }
                else {
                  // Extract value from displayed text (e.g. "A - White" => "A")
                  const match = answer.match(/^(\d+|[A-Z])\s*-/);
                  if (match) {
                    values.push(match[1]);
                  }
                }
              }
            });
            setChecklistValues(values);
          } else {
            setChecklistValues([]);
          }
          break;
        case FormControlNames.DATEFIELD:
          // if (answerValue && answerValue !== 'Not Available') {
          //   try {
          //     const dateValue = new Date(answerValue);
          //     // Validate that the parsed date is valid
          //     if (!isNaN(dateValue.getTime())) {
          //       setDate(dateValue);
          //       setTempDate(dateValue); // Also update tempDate
          //     } else {
          //       handleError('Invalid date format');
          //     }
          //   } catch (e) {
          //     // Set to null if parsing fails
          //     setDate(null);
          //     setTempDate(new Date()); // Keep tempDate as valid Date
          //     handleError(`Invalid date format: ${e instanceof Error ? e.message : String(e)}`);
          //   }
          // }
          if (answerValue && answerValue !== 'Not Available') {
            try {
              const dateValue = new Date(answerValue);
              // Validate that the parsed date is valid
              if (!isNaN(dateValue.getTime())) {
                setDate(dateValue);
                setTempDate(dateValue); // Also update tempDate
              } else {
                // Invalid date format - silently handle
                setDate(null);
                setTempDate(new Date());
              }
            } catch (e) {
              // Set to null if parsing fails - silently handle
              setDate(null);
              setTempDate(new Date()); // Keep tempDate as valid Date
            }
          }
          else {
            // No valid date available, set to null
            setDate(null);
            setTempDate(new Date()); // Keep tempDate as valid Date for picker
          }
          break;
        case FormControlNames.NUMERICWITHUNITS:
          if (answerValue) {
            try {
              const parsed = JSON.parse(answerValue);
              setNumericWithUnitsValue(parsed);
            } catch (e) {
              handleError(`Invalid numeric format: ${e instanceof Error ? e.message : String(e)}`);
            }
          }
          break;
      }
    } catch (error) {
      handleError(`Error syncing state: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [item?.answer_text, item?.controlName]);

  const toggleDropdown = () => {
    try {
      setIsDropdownOpen(!isDropdownOpen);
    } catch (error) {
      handleError(`Error toggling dropdown: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Helper: toggles a value in `checklistValues` and updates answer_text
  const toggleChecklistValue = (val: string) => {
    try {
      const updatedValues = checklistValues.includes(val)
        ? checklistValues.filter(v => v !== val)
        : [...checklistValues, val];

      setChecklistValues(updatedValues);
      updateAnswerText(updatedValues);
    } catch (error) {
      handleError(`Error toggling checklist value: ${error instanceof Error ? error.message : String(error)}`);
    }
  };


  // Helper: updates the numeric with units values
  const updateNumericWithUnits = (field: 'value' | 'unit', newValue: string) => {
  try {
    // Validation for the 'value' field - only allow positive integers with max 3 digits
    if (field === 'value') {
      // Allow empty string (user might be clearing the field)
      if (newValue === '') {
        const updatedValue = {
          ...numericWithUnitsValue,
          [field]: newValue
        };
        setNumericWithUnitsValue(updatedValue);
        updateAnswerText(JSON.stringify(updatedValue));
        return;
      }

      // Remove any non-digit characters (including decimal points)
      let cleanedValue = newValue.replace(/[^\d]/g, '');
      
      // Truncate to 3 digits if longer
      if (cleanedValue.length > 3) {
        cleanedValue = cleanedValue.substring(0, 3);
      }
      
      // If the cleaned value is empty or zero, reject silently
      if ( cleanedValue === '0') {
        return; // Don't update if invalid
      }

      const updatedValue = {
        ...numericWithUnitsValue,
        [field]: cleanedValue
      };
      setNumericWithUnitsValue(updatedValue);
      updateAnswerText(JSON.stringify(updatedValue));
    } else {
      // For unit field, allow as before
      const updatedValue = {
        ...numericWithUnitsValue,
        [field]: newValue
      };
      setNumericWithUnitsValue(updatedValue);
      updateAnswerText(JSON.stringify(updatedValue));
    }

    // Clear any previous error if validation passes
    setInputError(null);
  } catch (error) {
    handleError(`Error updating numeric with units: ${error instanceof Error ? error.message : String(error)}`);
  }
};
  

  switch (item.controlName) {
    case FormControlNames.LABEL:
      return null;

    case FormControlNames.INPUTTEXTFIELD:
      return (
        <View>

          <TextInput
            style={[
              styles.textInput,
              inputError ? styles.inputError : null,
              !isAnswered ? styles.inputNotAnswered : null
            ]}
            editable={!isDisabledFields}
            placeholder={item.placeholder}
            value={textValue}
            onChangeText={(text) => {
              try {
                // Optional: Add validation here if needed
                if (item.dataType === 'number' && text !== '' && isNaN(Number(text))) {
                  handleError('Please enter a valid number');
                  return;
                }

                setTextValue(text);
                updateAnswerText(text);
              } catch (error) {
                handleError(`Error updating text: ${error instanceof Error ? error.message : String(error)}`);
              }
            }}
            placeholderTextColor={'#9CA3AF'}
            keyboardType={item.dataType === 'number' ? 'numeric' : 'default'}
          />
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );


    case FormControlNames.NUMERICWITHUNITS:
      if (isWeb) {
        return (
          <View>
            <View style={styles.numericWithUnitsContainer}>
              {/* <Text style={styles.childDescription}>Unit Suffix</Text> */}
              <input
                type="number"

                style={{
                  ...styles.numericInput,
                  ...(inputError ? styles.inputErrorWeb : {}),
                  ...(!isAnswered ? styles.inputNotAnsweredWeb : {}),
                  height: 38,
                  boxSizing: 'border-box' as 'border-box',
                  outlineColor: '#4C51BF'
                }}
                placeholder={item.placeholder || 'Value'}
                value={numericWithUnitsValue.value}
                onChange={(e) => {
                  try {
                    updateNumericWithUnits('value', e.target.value);
                  } catch (error) {
                    handleError(`Error updating numeric value: ${error instanceof Error ? error.message : String(error)}`);
                  }
                }}
              />
              {/* <Text style={styles.childDescription}>Unit Prefix(optional)</Text>
              <input
                type="text"
                style={{
                  ...styles.unitInput,
                  ...(inputError ? styles.inputErrorWeb : {}),
                  ...(!isAnswered ? styles.inputNotAnsweredWeb : {}),
                  height: 38,
                  boxSizing: 'border-box' as 'border-box',
                  outlineColor: '#4C51BF'
                }}
                placeholder={item.unitPlaceholder || 'Units'}
                value={numericWithUnitsValue.unit}
                onChange={(e) => {
                  try {
                    updateNumericWithUnits('unit', e.target.value);
                  } catch (error) {
                    handleError(`Error updating unit value: ${error instanceof Error ? error.message : String(error)}`);
                  }
                }}
              /> */}
            </View>
            {!isAnswered && <NotAnsweredWarning />}
            <ErrorDisplay message={inputError} />
          </View>
        );
      } else {
        return (
          <View>
            <View style={styles.numericWithUnitsContainer}>
              {/* <Text style={styles.childDescription}>Unit Suffix</Text> */}
              <TextInput
                style={[
                  styles.numericInput,
                  inputError ? styles.inputError : null,
                  !isAnswered ? styles.inputNotAnswered : null
                ]}
                placeholder={item.placeholder || 'Value'}
                value={numericWithUnitsValue.value}
                editable={!isDisabledFields}
                onChangeText={(text) => {
                  try {
                    // Additional client-side validation for immediate feedback
                    if (text !== '' && (isNaN(parseFloat(text)) || parseFloat(text) <= 0)) {
                      // Still call updateNumericWithUnits to show error, but don't allow the change
                      updateNumericWithUnits('value', text);
                      return;
                    }
                    updateNumericWithUnits('value', text);
                  } catch (error) {
                    handleError(`Error updating numeric value: ${error instanceof Error ? error.message : String(error)}`);
                  }
                }}
                placeholderTextColor={'#9CA3AF'}
                keyboardType="numeric"
              />
              {/* <Text style={styles.childDescription}>Unit Prefix(optional)</Text>
              <TextInput
                style={[
                  styles.unitInput,
                  inputError ? styles.inputError : null,
                  !isAnswered ? styles.inputNotAnswered : null
                ]}
                placeholder={item.unitPlaceholder || 'Units'}
                value={numericWithUnitsValue.unit}
                onChangeText={(text) => {
                  try {
                    updateNumericWithUnits('unit', text);
                  } catch (error) {
                    handleError(`Error updating unit value: ${error instanceof Error ? error.message : String(error)}`);
                  }
                }}
                placeholderTextColor={'#9CA3AF'}
              /> */}
            </View>
            {!isAnswered && <NotAnsweredWarning />}
            <ErrorDisplay message={inputError} />
          </View>
        );
      }

    case FormControlNames.INPUTMULTILINE:
      return (
        <View>
          <TextInput
            style={[
              styles.textInput,
              { height: (item.rows || 3) * 32 },
              inputError ? styles.inputError : null,
              !isAnswered ? styles.inputNotAnswered : null
            ]}
            placeholder={item.placeholder}
            value={textValue}
            editable={!isDisabledFields}
            onChangeText={(text) => {
              try {
                setTextValue(text);
                updateAnswerText(text);
              } catch (error) {
                handleError(`Error updating multi-line text: ${error instanceof Error ? error.message : String(error)}`);
              }
            }}
            multiline={true}
            placeholderTextColor={'#9CA3AF'}
            numberOfLines={item.rows || 3}
          />
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );
    case FormControlNames.CHECKBOX:
      return (
        <View>
          <TouchableOpacity
            style={styles.checkboxRow}
            onPress={() => {
              if (disabled || isDisabledFields) return;
              try {
                const newValue = !checkValue;
                setCheckValue(newValue);
                updateAnswerText(newValue);
              } catch (error) {
                handleError(`Error toggling checkbox: ${error instanceof Error ? error.message : String(error)}`);
              }
            }}
            activeOpacity={disabled ? 1 : 0.7}
            disabled={disabled || isDisabledFields}
          >
            <View style={[
              styles.checkbox,
              checkValue ? styles.checkboxChecked : styles.checkboxUnchecked,
              !isAnswered ? styles.checkboxNotAnswered : null,
              disabled ? styles.disabledControl : null
            ]}>
              {checkValue && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
            <Text style={[
              styles.labelText,
              disabled ? styles.disabledText : null
            ]}>{item.placeholder}</Text>
          </TouchableOpacity>
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );

    case FormControlNames.CHECKLIST:
      return (
        <View>
          {Array.isArray(item.items) ? item.items.map(chk => {
            if (!chk || typeof chk.value === 'undefined') {
              return null; // Skip invalid items
            }

            const isChecked = checklistValues.includes(chk.value);
            return (
              <TouchableOpacity
                key={chk.value}
                style={styles.checkboxRow}
                onPress={() => {
                  if (disabled || isDisabledFields) return;
                  toggleChecklistValue(chk.value);
                }}
                activeOpacity={disabled ? 1 : 0.7}
                disabled={disabled || isDisabledFields}
              >
                <View style={[
                  styles.checkbox,
                  isChecked ? styles.checkboxChecked : styles.checkboxUnchecked,
                  !isAnswered ? styles.checkboxNotAnswered : null,
                  disabled ? styles.disabledControl : null
                ]}>
                  {isChecked && (
                    <Ionicons name="checkmark" size={16} color="white" />
                  )}
                </View>
                <Text style={[
                  styles.labelText,
                  disabled ? styles.disabledText : null
                ]}>{chk.label}</Text>
              </TouchableOpacity>
            );
          }) : (
            <Text style={styles.errorText}>No items available for checklist</Text>
          )}
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );
    case FormControlNames.RADIOGROUP:
      return (
        <View>
          {Array.isArray(item.items) ? item.items.map(radioItem => {
            // console.log(radioItem.value,selectedRadio)
            if (!radioItem || typeof radioItem.value === 'undefined') {
              return null; // Skip invalid items
            }

            return (
              <TouchableOpacity
                key={radioItem.value}
                style={styles.radioRow}
                onPress={() => {
                  if (disabled || isDisabledFields) return;
                  console.log("Value :", radioItem.value)
                  try {
                    setSelectedRadio(radioItem.value);
                    updateAnswerText(radioItem.value);
                  } catch (error) {
                    handleError(`Error selecting radio option: ${error instanceof Error ? error.message : String(error)}`);
                  }
                }}
                disabled={disabled || isDisabledFields}
              >
                <View
                  style={[
                    styles.radioOuter,
                    selectedRadio === radioItem.value && styles.radioOuterSelected,
                    !isAnswered ? styles.radioNotAnswered : null,
                    disabled ? styles.disabledControl : null,
                  ]}
                >
                  {selectedRadio === radioItem.value && (
                    <View style={styles.radioInner} />
                  )}
                </View>
                <Text style={[
                  styles.labelText,
                  disabled ? styles.disabledText : null
                ]}>{radioItem.label}</Text>
              </TouchableOpacity>
            );
          }) : (
            <Text style={styles.errorText}>No items available for radio group</Text>
          )}
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );
    case FormControlNames.SELECTDROPDOWN:
      return (
        <View>
          <TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.dropdown,
                inputError ? styles.inputError : null,
                !isAnswered ? styles.inputNotAnswered : null
              ]}
              onPress={toggleDropdown}
            >
              <Text style={styles.dropdownText}>
                {item.answer_text &&
                  Array.isArray(item.answer_text) &&
                  item.answer_text.length > 0 &&
                  item.answer_text[0] !== 'Not Available'
                  ? item.answer_text[0]
                  : 'Select Answer'}
              </Text>
              <Ionicons
                name={isDropdownOpen ? "chevron-up" : "chevron-down"}
                size={20}
                color="#888"
              />
            </TouchableOpacity>
            {isDropdownOpen && (
              <View style={styles.dropdownOptions}>
                {Array.isArray(item.items) ? item.items.map((option) => {
                  if (!option || typeof option.id === 'undefined') {
                    return null; // Skip invalid items
                  }

                  return (
                    <TouchableOpacity
                      key={option.id}
                      style={styles.optionItem}
                      onPress={() => {
                        try {
                          updateAnswerText(option.id);
                          setIsDropdownOpen(false);
                        } catch (error) {
                          handleError(`Error selecting dropdown option: ${error instanceof Error ? error.message : String(error)}`);
                        }
                      }}
                    >
                      <Text style={styles.optionText}>{option.label}</Text>
                    </TouchableOpacity>
                  );
                }) : (
                  <Text style={styles.errorText}>No options available</Text>
                )}
              </View>
            )}
          </TouchableOpacity>
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );
    case FormControlNames.SCORECALCULATOR:

      if (containerItems.length > 0) {
        return (
          <ScoreCalculatorDisplay
            item={item}
            allAnswers={allAnswers}
            containerItems={containerItems}
            onChange={onChange}
          />
        );
      } else {
        return (
          <>
            <Text style={styles.scoreCardTitle}>Included Questions:</Text>
            {selectedScoreItems.map((scoreItem, index) => (
              <View style={styles.scoreCardContainer}>
                <Text key={index} style={styles.scoreCardItem}>
                  {`${scoreItem.questionCode}: ${scoreItem.labelName}`}
                </Text>
              </View>
            ))}
            {selectedScoreItems.length === 0 && (
              <Text style={styles.noQuestionsText}>No questions included</Text>
            )}
          </>
        );
      }
    case FormControlNames.DATEFIELD:
      const handleDateChange = (newDate: Date) => {
        try {
          // Validate date
          if (isNaN(newDate.getTime())) {
            handleError('Invalid date selected');
            return;
          }

          setDate(newDate);
          // When updating the answer_text, we should store in a consistent format (ISO)
          updateAnswerText(newDate.toISOString());
          // Update isAnswered state immediately
          setIsAnswered(true);
        } catch (error) {
          handleError(`Error updating date: ${error instanceof Error ? error.message : String(error)}`);
        }
      };
      // Modified webDatepicker function with Safari compatibility
      const webDatepicker = () => {
        try {
          // Create a unique ID using the item's id or some other unique property
          const uniqueId = `date-input-${item.id || Math.random().toString(36).substr(2, 9)}`;

          // Format for display (mm/dd/yyyy)
          const displayValue = formatDateMMDDYYYY(date);

          // Format for input element (yyyy-mm-dd)
          let inputDateValue = '';
          try {
            // if (!isNaN(date.getTime())) {
            //   inputDateValue = date.toISOString().split('T')[0];
            // }
            if (date != "" && date != null) {
              inputDateValue = date.toISOString().split('T')[0];
            }
          } catch (e) {
            // Use empty string if toISOString fails
            handleError(`Error formatting date: ${e instanceof Error ? e.message : String(e)}`);
          }

          // Safari detection - check if it's iOS or iPadOS
          const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
            /iPad|iPhone|iPod/.test(navigator.userAgent);

          return (
            <View>
              <div
                style={{
                  ...styles.dateFieldContainerWeb,
                  ...(!isAnswered ? styles.dateFieldNotAnsweredWeb : {})
                }}
                onClick={() => {
                  try {
                    // Find the input by its unique ID
                    const dateInput = document.getElementById(uniqueId) as HTMLInputElement;

                    if (dateInput) {
                      if (!isSafari && typeof dateInput.showPicker === 'function') {
                        // For browsers that support showPicker()
                        dateInput.showPicker();
                      } else {
                        // For Safari and other browsers without showPicker()
                        // Focus and click the input programmatically
                        dateInput.focus();
                        dateInput.click();

                        // Add a small delay before focusing again (helps on some Safari versions)
                        setTimeout(() => {
                          dateInput.focus();
                        }, 10);
                      }
                    }
                  } catch (error) {
                    handleError(`Error showing date picker: ${error instanceof Error ? error.message : String(error)}`);
                  }
                }}
              >
                <View style={styles.dateDisplayContent}>
                  <Text style={styles.dateDisplayText}>
                    {displayValue || 'Select Date'}
                  </Text>
                  <Ionicons name="calendar-outline" size={20} color="#666" />
                </View>
                <input
                  id={uniqueId}
                  type="date"
                  style={{
                    ...styles.hiddenDateInput,
                    // For Safari, make the input slightly visible but transparent
                    // This helps with touch events on iOS
                    ...(isSafari ? {
                      opacity: 0.01,
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      width: '100%',
                      height: '100%',
                      zIndex: 1
                    } : {})
                  }}
                  value={inputDateValue}
                  onChange={(e) => {
                    try {
                      if (e.target.value) {
                        const newDate = new Date(e.target.value);
                        handleDateChange(newDate);
                      }
                    } catch (error) {
                      handleError(`Error parsing date: ${error instanceof Error ? error.message : String(error)}`);
                    }
                  }}
                />
              </div>
              {!isAnswered && <NotAnsweredWarning />}
              <ErrorDisplay message={inputError} />
            </View>
          );
        } catch (error) {
          handleError(`Error rendering web datepicker: ${error instanceof Error ? error.message : String(error)}`);
          return <ErrorDisplay message="Error rendering date picker" />;
        }
      };

      
      const nativeDatePicker = () => {
        const handleDateConfirm = (selectedDate: Date) => {
          try {
            console.log('Date confirmed from bottom sheet:', selectedDate);
            setPickerVisible(false);
            handleDateChange(selectedDate);
          } catch (error) {
            handleError(`Error confirming date: ${error instanceof Error ? error.message : String(error)}`);
          }
        };

        const handleDateCancel = () => {
          try {
            console.log('Date picker cancelled');
            setPickerVisible(false);
            // Reset tempDate appropriately
            setTempDate(date || new Date());
          } catch (error) {
            handleError(`Error cancelling date picker: ${error instanceof Error ? error.message : String(error)}`);
          }
        };

        const onDatePickerChange = (event: any, selectedDate?: Date) => {
          try {
            if (Platform.OS === 'android') {
              setPickerVisible(false);
              if (event.type === 'set' && selectedDate) {
                handleDateChange(selectedDate);
              }
            } else {
              if (selectedDate) {
                setTempDate(selectedDate);
              }
            }
          } catch (error) {
            handleError(`Error in date picker change: ${error instanceof Error ? error.message : String(error)}`);
          }
        };

        return (
          <View>
            <TouchableOpacity
              style={[
                styles.dateFieldContainer,
                // Add input error styling
                inputError ? styles.inputError : null,
                // Add not answered styling (YELLOW BORDER)
                !isAnswered ? styles.inputNotAnswered : null
              ]}
              onPress={() => {
                try {
                  console.log('Calendar icon clicked, opening date picker...');
                  // Set tempDate to current date if no date is selected, or use the existing date
                  setTempDate(date || new Date());
                  setPickerVisible(true);
                } catch (error) {
                  handleError(`Error showing date picker: ${error instanceof Error ? error.message : String(error)}`);
                }
              }}
              activeOpacity={0.7}
              disabled={isDisabledFields}
            >
              <View style={styles.dateDisplayContent}>
                <Text style={styles.dateDisplayText}>
                  {date ? formatDateMMDDYYYY(date) : 'Select Date'}
                </Text>
                <Ionicons name="calendar-outline" size={20} color="#666" />
              </View>
            </TouchableOpacity>

            {/* Android DateTimePicker */}
            {Platform.OS === 'android' && isPickerVisible && (
              <DateTimePicker
                value={date || new Date()}
                mode="date"
                display="default"
                onChange={onDatePickerChange}
              />
            )}

            {/* iOS Modal with DateTimePicker */}
            {Platform.OS === 'ios' && (
              <Modal
                visible={isPickerVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={handleDateCancel}
              >
                <View style={styles.modalOverlay}>
                  <TouchableOpacity
                    style={styles.overlayTouchable}
                    activeOpacity={1}
                    onPress={handleDateCancel}
                  >
                    <View style={styles.bottomSheet}>
                      <TouchableOpacity activeOpacity={1} onPress={() => { }}>
                        <View style={styles.bottomSheetHeader}>
                          <TouchableOpacity onPress={handleDateCancel}>
                            <Text style={styles.cancelButton}>Cancel</Text>
                          </TouchableOpacity>
                          <Text style={styles.headerTitle}>Select Date</Text>
                          <TouchableOpacity onPress={() => handleDateConfirm(tempDate)}>
                            <Text style={styles.confirmButton}>Confirm</Text>
                          </TouchableOpacity>
                        </View>

                        <View style={styles.datePickerContainer}>
                          <DateTimePicker
                            value={tempDate}
                            mode="date"
                            display="spinner"
                            onChange={onDatePickerChange}
                            style={styles.datePicker}
                          />
                        </View>

                        <View style={styles.actionButtons}>
                          <TouchableOpacity
                            style={styles.confirmButtonContainer}
                            onPress={() => handleDateConfirm(tempDate)}
                          >
                            <Text style={styles.confirmButtonText}>Confirm</Text>
                          </TouchableOpacity>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Add the Not Answered warning - THIS IS THE KEY ADDITION */}
            {!isAnswered && <NotAnsweredWarning />}
            <ErrorDisplay message={inputError} />
          </View>
        );
      };

      return isWeb ? webDatepicker() : nativeDatePicker();

    case FormControlNames.TIMEFIELD:
      return (
        <View>
          <input
            type="time"
            style={{
              ...styles.placeholderBox,
              ...(inputError ? styles.inputErrorWeb : {}),
              ...(!isAnswered ? styles.inputNotAnsweredWeb : {})
            }}
            value={getFirstAnswer() || ''}
            onChange={(e) => {
              try {
                updateAnswerText(e.target.value);
              } catch (error) {
                handleError(`Error updating time: ${error instanceof Error ? error.message : String(error)}`);
              }
            }}
          />
          {!isAnswered && <NotAnsweredWarning />}
          <ErrorDisplay message={inputError} />
        </View>
      );

    case FormControlNames.TOGGLE:
      return (
        <View>
          <View style={styles.switchRow}>
            <Switch
              value={switchValue}
              onValueChange={(value) => {
                try {
                  setSwitchValue(value);
                  updateAnswerText(value);
                } catch (error) {
                  handleError(`Error updating toggle: ${error instanceof Error ? error.message : String(error)}`);
                }
              }}
              trackColor={{ false: "#f4f3f4", true: THEME_COLOR }}
              thumbColor={switchValue ? "#ffffff" : "#f4f3f4"}
              ios_backgroundColor="#3e3e3e"
            />
            <Text style={styles.labelText}>
              {item.placeholder || 'Toggle Label'}
            </Text>
          </View>
          {/* For toggle switches, we typically don't show "Not Answered" warning since 
                both ON and OFF are valid states, unlike other form fields */}
          <ErrorDisplay message={inputError} />
        </View>
      ); default:
      return null;
  }
};

export default RenderItem;

const styles = StyleSheet.create({
  childDescription: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#7C7887',
    marginBottom: 6,
  },
  // Common styles
  errorMessageContainer: {
    marginTop: 4,
    marginBottom: 8,
  },
  errorText: {
    color: 'red',
    fontSize: 14,
    fontFamily: 'PlusJakartaSans_400Regular',
  },
  warningContainer: {
    marginTop: 2,
    marginBottom: 8,
  },
  warningText: {
    color: WARNING_COLOR, // WARNING_COLOR = '#FF9800'
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
  },

  // Text Input styles
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#000',
    marginBottom: 8,
  },
  inputError: {
    borderColor: 'red',
    borderWidth: 2,
  },
  inputErrorWeb: {
    borderColor: 'red',
    borderWidth: '2px',
  },
  inputNotAnswered: {
    borderColor: WARNING_COLOR,
    borderWidth: 2,
  },
  inputNotAnsweredWeb: {
    borderColor: WARNING_COLOR,
    borderWidth: '2px',
  },

  // Date Field styles
  dateFieldContainer: {
    position: 'relative',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
  },
  dateFieldContainerWeb: {
    position: 'relative',
    border: '1px solid #D1D5DB',
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
    cursor: 'pointer',
  },
  dateFieldNotAnsweredWeb: {
    border: `2px solid ${WARNING_COLOR}`,
  },
  hiddenDateInput: {
    opacity: 0,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    cursor: 'pointer',
    zIndex: -1, // Place behind the content
  },
  dateDisplayContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateDisplayText: {
    fontFamily: 'Poppins_400Regular',
    color: '#000',
  },

  // Numeric with Units styles
  numericWithUnitsContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  numericInput: {
    flex: 0.5,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#000',
    // marginRight: 8,
  },
  unitInput: {
    flex: 0.4,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 10,
    fontFamily: 'Poppins_400Regular',
    color: '#000',
  },

  // Checkbox styles
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    //marginBottom: 8,
    //paddingEnd: 20
    paddingRight: 20,
    paddingBottom: 16,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: THEME_COLOR,
  },
  checkboxUnchecked: {
    borderWidth: 1,
    borderColor: '#999',
  },
  checkboxNotAnswered: {
    borderColor: WARNING_COLOR,
    borderWidth: 2,
  },
  disabledControl: {
    opacity: 0.4,
  },
  disabledText: {
    color: '#9CA3AF',
    opacity: 0.6,
  },
  labelText: {
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
    paddingLeft: 10
  },

  // Radio button styles
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: 8,
    // paddingEnd: 20
    paddingRight: 20,
    paddingBottom: 16,
  },
  radioOuter: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: THEME_COLOR,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioOuterSelected: {
    borderColor: THEME_COLOR,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: THEME_COLOR,
  },
  radioNotAnswered: {
    borderColor: WARNING_COLOR,
    borderWidth: 3,
  },

  // Dropdown styles
  dropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    padding: 12,
    backgroundColor: '#fff',
  },
  dropdownText: {
    fontSize: 16,
    color: '#333',
  },
  dropdownOptions: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    marginTop: 5,
    backgroundColor: '#fff',
  },
  optionItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  optionText: {
    fontSize: 16,
  },

  // Switch styles
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },

  // Time Field styles
  placeholderBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },

  // Other form element styles
  selectBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectBoxText: {
    color: '#1F2937',
    fontFamily: 'PlusJakartaSans_400Regular',
  },
  uploadBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  signatureBox: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#4C51BF',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    marginBottom: 8,
  },
  signatureText: {
    color: '#4C51BF',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },

  // score calculation
  scoreCardContainer: {
    // backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E9ECEF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  scoreCardTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 14,
    color: '#495057',
    marginBottom: 12,
  },
  scoreCardItem: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#6C757D',
    marginBottom: 4,
    paddingLeft: 4,
  },
  noQuestionsText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#6C757D',
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  overlayTouchable: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
    maxHeight: Dimensions.get('window').height * 0.6,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cancelButton: {
    color: '#6B7280',
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    fontFamily: 'Poppins_600SemiBold',
  },
  confirmButton: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  datePickerContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    alignItems: 'center',
  },
  datePicker: {
    width: '100%',
    height: 200,
  },
  selectedDateContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  selectedDateLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Poppins_400Regular',
    marginBottom: 4,
  },
  selectedDateText: {
    fontSize: 18,
    color: '#1F2937',
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 20,
    gap: 12,
  },
  cancelButtonContainer: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
  confirmButtonContainer: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    borderRadius: 30,
    padding: 12,
    alignItems: 'center',
  },

  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
  },
});