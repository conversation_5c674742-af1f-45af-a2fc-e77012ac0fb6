import React, { FC, useState, useEffect, useMemo } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    TextInput,
    Modal,
} from 'react-native';
import {
    FormLayoutComponentChildrenType,
    FormLayoutComponentsType,
} from '../types/FormTemplateTypes';
import { Feather } from '@expo/vector-icons';

interface SkipLogicComponentProps {
    item: FormLayoutComponentChildrenType;
    formLayoutComponents: FormLayoutComponentsType[];
    onSkipLogicChange: (skipLogic: any) => void;
}

const SkipLogicComponent: FC<SkipLogicComponentProps> = ({
    item,
    formLayoutComponents,
    onSkipLogicChange,
}) => {
    const [expandedOption, setExpandedOption] = useState<string>('');
    const [availableQuestions, setAvailableQuestions] = useState<FormLayoutComponentChildrenType[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');

    // Filter questions based on search query
    const filteredQuestions = useMemo(() => {
        if (!searchQuery.trim()) {
            return availableQuestions;
        }

        const query = searchQuery.toLowerCase();
        return availableQuestions.filter(question =>
            question.questionCode?.toLowerCase().includes(query) ||
            question.labelName?.toLowerCase().includes(query)
        );
    }, [availableQuestions, searchQuery]);

    // Get all available questions that can be skipped to
    const getAvailableQuestions = (): FormLayoutComponentChildrenType[] => {
        const questions: FormLayoutComponentChildrenType[] = [];

        const processItems = (items: any[]) => {
            items.forEach(itemObj => {
                if (!itemObj.isContainer) {
                    const question = itemObj as FormLayoutComponentChildrenType;
                    // Don't include the current question or questions without questionCode
                    if (question.id !== item.id && question.questionCode) {
                        questions.push(question);
                    }
                } else {
                    // Recursively process nested containers
                    if (itemObj.items) {
                        processItems(itemObj.items);
                    }
                }
            });
        };

        formLayoutComponents.forEach(layout => {
            if (layout.items) {
                processItems(layout.items);
            }
        });

        return questions;
    };

    useEffect(() => {
        setAvailableQuestions(getAvailableQuestions());
    }, [formLayoutComponents, item]);

    const handleToggleDropdown = (optionValue: string) => {
        if (expandedOption === optionValue) {
            setExpandedOption('');
            setSearchQuery(''); // Reset search when closing
        } else {
            setExpandedOption(optionValue);

            // Find the option by value to get its label
            const option = item.items?.find(opt => opt.value === optionValue);
            if (option?.label) {
                // Check if option label contains "Skip to" and extract question code
                const skipToMatch = option.label.match(/Skip to ([A-Z0-9]+)/);
                if (skipToMatch) {
                    setSearchQuery(skipToMatch[1]); // Extract the question code (e.g., "B200")
                } else {
                    setSearchQuery(''); // No "Skip to" found, keep search empty
                }
            } else {
                setSearchQuery(''); // No option found, keep search empty
            }
        }
    };

    const handleQuestionSelect = (optionValue: string, targetQuestion: FormLayoutComponentChildrenType) => {
        const currentSkipLogic = item.skipLogic || {};
        const updatedSkipLogic = {
            ...currentSkipLogic,
            [optionValue]: {
                targetQuestionId: targetQuestion.id,
                targetQuestionCode: targetQuestion.questionCode,
                targetQuestionLabel: targetQuestion.labelName,
            },
        };

        onSkipLogicChange(updatedSkipLogic);
        setExpandedOption('');
        setSearchQuery(''); // Reset search when question is selected
    };

    const handleRemoveSkipLogic = (optionValue: string) => {
        const currentSkipLogic = item.skipLogic || {};
        const updatedSkipLogic = { ...currentSkipLogic };
        delete updatedSkipLogic[optionValue];

        onSkipLogicChange(updatedSkipLogic);
    };

    const handleCloseModal = () => {
        setExpandedOption('');
        setSearchQuery(''); // Reset search when modal is closed
    };

    const getSkipLogicTarget = (optionValue: string) => {
        const skipLogic = item.skipLogic || {};
        const target = skipLogic[optionValue];
        if (target) {
            return availableQuestions.find(q => q.id === target.targetQuestionId);
        }
        return null;
    };

    if (!item.items || item.items.length === 0) {
        return (
            <View style={styles.container}>
                <Text style={styles.sectionTitle}>Skip Logic</Text>
                <Text style={styles.noOptionsText}>No options available for skip logic</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Text style={styles.sectionTitle}>Skip Logic</Text>

            <ScrollView style={styles.optionsList}>
                {item.items.map((option) => {
                    const targetQuestion = getSkipLogicTarget(option.value);
                    const isExpanded = expandedOption === option.value;

                    return (
                        <View key={option.value} style={styles.optionContainer}>
                            <View style={styles.optionRow}>
                                <View style={styles.optionInfo}>
                                    <Text style={styles.optionLabel}>{option.label}</Text>
                                    {targetQuestion && (
                                        <Text style={styles.targetQuestion}>
                                            → Skip to: {targetQuestion.labelName}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.optionActions}>
                                    {!targetQuestion ? (
                                        <TouchableOpacity
                                            style={styles.configureButton}
                                            onPress={() => handleToggleDropdown(option.value)}
                                        >
                                            <Text style={styles.configureButtonText}>Skip to</Text>
                                            
                                            <Feather name="chevron-down" size={16} color="#FFFFFF" />
                                        </TouchableOpacity>
                                    ) : (
                                        <View style={styles.configuredActions}>
                                            <TouchableOpacity
                                                style={styles.changeButton}
                                                onPress={() => handleToggleDropdown(option.value)}
                                            >
                                                <Text style={styles.changeButtonText}>Change</Text>
                                                <Feather name="chevron-down" size={16} color="#FFFFFF" />
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={styles.removeButton}
                                                onPress={() => handleRemoveSkipLogic(option.value)}
                                            >
                                                <Text style={styles.removeButtonText}>Remove</Text>
                                            </TouchableOpacity>
                                        </View>
                                    )}
                                </View>
                            </View>

                            {/* Search Modal */}
                            <Modal
                                transparent={true}
                                animationType="fade"
                                visible={isExpanded}
                                onRequestClose={handleCloseModal}
                            >
                                <View style={styles.modalOverlay}>
                                    <View style={styles.searchModal}>
                                        <View style={styles.modalHeader}>
                                            <Text style={styles.modalTitle}>Select Target Question</Text>
                                            <TouchableOpacity
                                                style={styles.closeButton}
                                                onPress={handleCloseModal}
                                            >
                                                <Text style={styles.closeButtonText}>✕</Text>
                                            </TouchableOpacity>
                                        </View>

                                        <View style={styles.searchContainer}>
                                            <TextInput
                                                style={styles.searchInput}
                                                placeholder="Search questions..."
                                                value={searchQuery}
                                                onChangeText={setSearchQuery}
                                                autoFocus={true}
                                            />
                                        </View>

                                        <ScrollView style={styles.questionList}>
                                            {filteredQuestions.map((question) => (
                                                <TouchableOpacity
                                                    key={question.id}
                                                    style={styles.questionItem}
                                                    onPress={() => handleQuestionSelect(option.value, question)}
                                                >
                                                    <Text style={styles.questionCode}>{question.questionCode}</Text>
                                                    <Text style={styles.questionLabel}>{question.labelName}</Text>
                                                </TouchableOpacity>
                                            ))}
                                            {filteredQuestions.length === 0 && (
                                                <Text style={styles.noResultsText}>No questions found</Text>
                                            )}
                                        </ScrollView>
                                    </View>
                                </View>
                            </Modal>
                        </View>
                    );
                })}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginTop: 16,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1F2937',
        marginBottom: 12,
    },
    noOptionsText: {
        fontSize: 14,
        color: '#6B7280',
        fontStyle: 'italic',
    },
    optionsList: {
        maxHeight: 400,
    },
    optionContainer: {
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    optionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 12,
    },
    optionInfo: {
        flex: 1,
    },
    optionLabel: {
        fontSize: 14,
        color: '#1F2937',
        fontWeight: '500',
    },
    targetQuestion: {
        fontSize: 12,
        color: '#6B7280',
        marginTop: 2,
    },
    optionActions: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    configureButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#4C51BF',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        gap: 4,
    },
    configureButtonText: {
        color: '#FFFFFF',
        fontSize: 12,
        fontWeight: '500',
    },
    chevron: {
        // React Native doesn't support CSS transitions
    },
    chevronRotated: {
        transform: [{ rotate: '180deg' }],
    },
    configuredActions: {
        flexDirection: 'row',
        gap: 8,
    },
    changeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F59E0B',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
        gap: 4,
    },
    changeButtonText: {
        color: '#FFFFFF',
        fontSize: 11,
        fontWeight: '500',
    },
    removeButton: {
        backgroundColor: '#EF4444',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
    },
    removeButtonText: {
        color: '#FFFFFF',
        fontSize: 11,
        fontWeight: '500',
    },
    dropdownContainer: {
        backgroundColor: '#F9FAFB',
        padding: 12,
        marginHorizontal: 12,
        marginBottom: 8,
        borderRadius: 6,
        borderWidth: 1,
        borderColor: '#E5E7EB',
    },
    dropdownTitle: {
        fontSize: 12,
        color: '#6B7280',
        marginBottom: 8,
        fontWeight: '500',
    },
    questionDropdown: {
        maxHeight: 200,
        marginBottom: 8,
    },
    questionItem: {
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    questionCode: {
        fontSize: 12,
        color: '#6B7280',
        fontWeight: '500',
    },
    questionLabel: {
        fontSize: 14,
        color: '#1F2937',
        marginTop: 2,
    },
    cancelButton: {
        alignSelf: 'flex-end',
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor: '#F3F4F6',
        borderRadius: 4,
    },
    cancelButtonText: {
        color: '#6B7280',
        fontSize: 12,
        fontWeight: '500',
    },
    // Search Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    searchModal: {
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        width: '90%',
        maxWidth: 500,
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
        margin: 'auto',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1F2937',
    },
    closeButton: {
        padding: 4,
    },
    closeButtonText: {
        fontSize: 18,
        color: '#6B7280',
    },
    searchContainer: {
        padding: 16,
        paddingBottom: 8,
    },
    searchInput: {
        borderWidth: 1,
        borderColor: '#D1D5DB',
        borderRadius: 6,
        paddingHorizontal: 12,
        paddingVertical: 8,
        fontSize: 14,
        color: '#1F2937',
        backgroundColor: '#FFFFFF',
    },
    questionList: {
        maxHeight: 300,
    },
    noResultsText: {
        textAlign: 'center',
        padding: 16,
        color: '#6B7280',
        fontSize: 14,
        fontStyle: 'italic',
    },
});

export default SkipLogicComponent; 