import { getSecureItem } from "@/src/utils/cryptoHelper";
import { apiClient } from "src/api/apiClient";
export const fetchRoles = async() => {
    let names = await getSecureItem("org");
    return apiClient
        .get(`v1/auth/roles?limit=20&page=1&x-tenant-id=${names}`)
        .then((response) => response.data)
        .catch((error) => {
            console.log("Error:", error);
            throw error.response?.data?.errorMessage || "Failed to Fetch Roles";
        });
}
export const fetchUserList = (limit = 20, page = 1,filters="") => {
    return apiClient
        .get(`v1/auth/user?limit=${limit}&page=${page}${filters}`)
        .then((response) => response.data)
        .catch((error) => {
            throw error.response?.data?.errorMessage || "Failed to Fetch Users";
        });
}
export const saveUser = (data: any) => {
    return apiClient
        .post(`v1/auth/user`, data)
        .then((response) => response.data)
        .catch((error) => {
            throw error.response?.data?.errorMessage || "User creation failed";
        });
}
export const updateUser = (data: any,userId:string) => {
    return apiClient
        .put(`v1/auth/user/${userId}`, data)
        .then((response) => response.data)
        .catch((error) => {
            throw error.response?.data?.errorMessage || "User Deleted failed";
        });
}
