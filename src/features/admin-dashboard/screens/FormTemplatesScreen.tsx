import React, { ReactElement, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Image,
  TextInput,
} from 'react-native';
// import {
//   ChevronLeft,
//   Pencil,
//   Wand2,
//   ClipboardList,
//   FileText,
//   Users,
//   Target,
//   Plus,
// } from 'lucide-react-native';
import { useResponsive } from 'src/hooks/useResponsive';
import { globalStyles } from 'src/styles';
import ThemedModal from 'src/components/themed-modal/ThemedModal';

export default function FormTemplatesScreen({
  navigation,
}: {
  navigation: any;
}) {
  const { isTabletOrMobileDevice } = useResponsive();

  const [hoverItemId, setHoverItemId] = useState(null);
  const [isModalVisible, setModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Example data for cards
  const templateItems = [
    // {
    //   id: 'soc',
    //   title: 'Start of Care',
    //   type: 'OASIS-E',
    //   shortTitle: 'SOC',
    //   description: 'Start of care – further visits planned',
    //   icon: <ClipboardList size={40} color="#4C51BF" />,
    // },
    // {
    //   id: 'roc',
    //   title: 'Resumption of Care',
    //   type: 'OASIS-E',
    //   shortTitle: 'ROC',
    //   description: 'Resumption of care (after inpatient stay)',
    //   icon: <ClipboardList size={40} color="#4C51BF" />,
    // },
    // {
    //   id: 'fu',
    //   title: 'Follow-up',
    //   type: 'OASIS-E',
    //   shortTitle: 'FU',
    //   description: 'Recertification (follow-up) assessment',
    //   icon: <ClipboardList size={40} color="#4C51BF" />,
    // },
    // {
    //   id: 'dc',
    //   title: 'Discharge from Agency',
    //   type: 'OASIS-E',
    //   shortTitle: 'DC',
    //   description: 'Not to an inpatient facility or Death at home (DAH)',
    //   icon: <ClipboardList size={40} color="#4C51BF" />,
    // },
    // {
    //   id: 'trn',
    //   title: 'Transfer to an Inpatient Facility',
    //   type: 'OASIS-E',
    //   shortTitle: 'TRN',
    //   description: 'Patient not discharged from agency',
    //   icon: <ClipboardList size={40} color="#4C51BF" />,
    // },
  ];

  const handleBack = () => {
    // Example: navigation.goBack() or navigation.replace('SomeScreen')
    navigation.goBack();
  };

  const handleItemPress = (itemId: string) => {
    // Navigate or do something with selected item
    console.log('User selected template item:', itemId);
    navigation.replace('FormCreation');
  };

  const handleStartFromScratch = () => {
    setModalVisible(true);
  };

  const renderModalContent = () => {
    return (
      <TextInput
        style={styles.input}
        placeholder={'Enter Form Name'}
        placeholderTextColor="#9CA3AF"
        value={inputValue}
        onChangeText={setInputValue}
      />
    );
  };

  const renderFormCreateModal = () => {
    return (
      <ThemedModal
        isVisible={isModalVisible}
        title="Enter Form Details"
        placeholder="Type here..."
        onClose={() => setModalVisible(false)}
        renderContent={renderModalContent}
        onSave={() => {
          setModalVisible(false);
          navigation.replace('FormCreation');
        }}
      />
    );
  };

  const renderAddForm = () => {
    return (
      <TouchableOpacity
        style={[
          styles.scratchCard,
          hoverItemId === 'new' && styles.templateCardHover,
        ]}
        onPress={handleStartFromScratch}
        onMouseEnter={() => setHoverItemId('new')}
        onMouseLeave={() => setHoverItemId(null)}
      >
        {/* <Plus size={40} color="#4C51BF" /> */}
        <Text style={styles.scratchText}>Start from Scratch</Text>
      </TouchableOpacity>
    );
  };

  const renderTemplates = () => {
    const templateItemElements: ReactElement[] = [];

    templateItemElements.push(renderAddForm());

    templateItems.forEach(item =>
      templateItemElements.push(
        <TouchableOpacity
          key={item.id}
          style={[
            styles.templateCard,
            hoverItemId === item.id && styles.templateCardHover,
          ]}
          onPress={() => handleItemPress(item.id)}
          onMouseEnter={() => setHoverItemId(item.id)}
          onMouseLeave={() => setHoverItemId(null)}
        >
          {/* Optional "New" badge in top-right corner */}
          {item.type && (
            <View style={styles.badgeContainer}>
              <Text style={styles.badgeText}>{item.type}</Text>
            </View>
          )}

          <View style={styles.iconContainer}>{item.icon}</View>

          <Text style={styles.cardTitle} numberOfLines={2}>
            {item.title}
          </Text>

          <View style={styles.tagContainer}>
            <View style={styles.tag}>
              <Text style={styles.tagText}>{item.shortTitle}</Text>
            </View>
          </View>

          <Text style={styles.cardDescription} numberOfLines={3}>
            {item.description}
          </Text>
        </TouchableOpacity>,
      ),
    );
    return templateItemElements;
  };

  return (
    <View style={styles.root}>
      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          {/* <ChevronLeft color="#1F2937" size={24} /> */}
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Form</Text>
      </View>

      <ScrollView
        style={styles.scroll}
        contentContainerStyle={styles.scrollContent}
      >
        <Text style={[styles.headerTitle, styles.subTitle]}>
          Choose From Templates
        </Text>

        <View
          style={[
            styles.gridContainer,
            isTabletOrMobileDevice && styles.gridContainerMobile,
          ]}
        >
          {renderTemplates()}
        </View>
      </ScrollView>
      {renderFormCreateModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#1F2937',
  },
  subTitle: {
    fontSize: 22,
    marginBottom: 40,
  },
  scroll: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    alignItems: 'center', // Centers grid horizontally
    justifyContent: 'center', // Centers grid vertically
    paddingVertical: 40,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center', // Centers grid content
    width: '60%', // Controls the width of the grid
  },
  gridContainerMobile: {
    justifyContent: 'center',
  },
  templateCard: {
    width: 260,
    height: 240,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 16,
    padding: 16,
    marginRight: 16,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
    position: 'relative',
  },
  badgeContainer: {
    position: 'absolute',
    right: 12,
    top: 12,
    backgroundColor: '#FDE68A', // a light highlight color
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 10,
  },
  badgeText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 12,
    color: '#111827',
  },
  iconContainer: {
    marginBottom: 12,
  },
  cardTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
    // minHeight: 48,
  },
  cardDescription: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#4B5563',
    textAlign: 'center',
    minHeight: 56,
    marginTop: 12,
  },
  scratchDescription: {
    color: '#4C51BF',
    marginTop: 24,
    paddingHorizontal: 16,
  },
  scratchCard: {
    width: 260,
    height: 240,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 2,
    borderColor: '#4C51BF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    marginBottom: 16,
  },
  scratchText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#4C51BF',
    marginTop: 8,
  },
  templateCardHover: {
    ...globalStyles.shadow,
    transform: [{ scale: 1.01 }],
  },
  tagContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#D1FAE5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    color: '#111827',
    minWidth: 50,
  },
  tagText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 12,
    textAlign: 'center',
  },
  shortTagText: {
    fontSize: 12,
    color: '#FFFFFF',
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: 'Poppins_400Regular',
    color: '#000',
    marginBottom: 16,
  },
});
