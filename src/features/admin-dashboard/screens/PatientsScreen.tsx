import { setPageTitle } from '@/src/utils/GeneralUtils';
// import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Text,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import DynamicTable from 'src/features/admin-dashboard/components/dynamic-table/DynamicTable';
import {
  getCliniciansList,
  getGridData,
  getPatientsList,
} from 'src/screens/login/api';

export default function PatientsScreen({ navigation }) {
  const [patients, setPatients] = useState([]);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalData, setTotalData] = useState(0);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  setPageTitle('Clients');

  const fetchPatients = (page = 1, limit = 10) => {
    setLoading(true);
    getPatientsList({ limit, page }).then(response => {
      if (response.status === 'ok') {
        setPatients(response.data);
        setTotalData(response.total_records_available);
        setLoading(false);
      }
    });
  };

  useEffect(() => {
    getGridData({ gridName: 'clientTable' }).then(response => {
      if (response.status === 'ok') {
        setColumns(response.data);
        fetchPatients(currentPage, itemsPerPage);
      }
    });
  }, []);

  // Handle row actions
  const handleEdit = item => {
    console.log('Edit item:', item);
    // Implement your edit logic here
  };

  const handleDelete = id => {
    console.log('Delete item ID:', id);
    // Implement your delete logic here
  };

  const handleDeactivate = id => {
    console.log('Deactivate item ID:', id);
    // Implement your deactivate logic here
  };

  const onSelectRow = patientData => {
    navigation.navigate('PatientDetails', { patientData, formConfig: columns });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchPatients(page, itemsPerPage);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    fetchPatients(1, newItemsPerPage);
  };

  const totalPages = Math.ceil(totalData / itemsPerPage);

  // Show initial loading screen only when columns are not loaded yet
  if (loading && columns.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4C51BF" />
        <Text style={styles.loadingText}>Loading Clients...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Always render DynamicTable, pass loading state to it */}
      <DynamicTable
        data={patients}
        allColumns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onDeactivate={handleDeactivate}
        isLoading={loading}
        onSelectRow={onSelectRow}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
      />

      {/* Pagination controls */}
      <View style={styles.paginationContainer}>
        <View style={styles.paginationInfo}>
          <Text style={styles.paginationText}>
            Showing{' '}
            {patients.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0}{' '}
            to {Math.min(currentPage * itemsPerPage, totalData)} of{' '}
            {totalData} entries
          </Text>
        </View>
        <View style={styles.paginationControls}>
          <TouchableOpacity
            style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
            onPress={() => handlePageChange(1)}
            disabled={currentPage === 1}
          >
            {/* <ChevronsLeft
              size={18}
              color={currentPage === 1 ? '#D1D5DB' : '#4B5563'}
            /> */}
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
            onPress={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            {/* <ChevronLeft
              size={18}
              color={currentPage === 1 ? '#D1D5DB' : '#4B5563'}
            /> */}
          </TouchableOpacity>

          <Text style={styles.paginationPageInfo}>
            Page {currentPage} of {totalPages || 1}
          </Text>

          <TouchableOpacity
            style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
            onPress={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            {/* <ChevronRight
              size={18}
              color={
                currentPage === totalPages || totalPages === 0
                  ? '#D1D5DB'
                  : '#4B5563'
              }
            /> */}
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
            onPress={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages || totalPages === 0}
          >
            {/* <ChevronsRight
              size={18}
              color={
                currentPage === totalPages || totalPages === 0
                  ? '#D1D5DB'
                  : '#4B5563'
              }
            /> */}
          </TouchableOpacity>
        </View>
        <View style={styles.perPageContainer}>
          <Text style={styles.perPageLabel}>Show:</Text>
          <TouchableOpacity
            style={[styles.perPageButton, itemsPerPage === 10 && styles.perPageButtonActive]}
            onPress={() => handleItemsPerPageChange(10)}
          >
            <Text
              style={
                itemsPerPage === 10
                  ? styles.perPageButtonTextActive
                  : styles.perPageButtonText
              }
            >
              10
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.perPageButton, itemsPerPage === 25 && styles.perPageButtonActive]}
            onPress={() => handleItemsPerPageChange(25)}
          >
            <Text
              style={
                itemsPerPage === 25
                  ? styles.perPageButtonTextActive
                  : styles.perPageButtonText
              }
            >
              25
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.perPageButton, itemsPerPage === 50 && styles.perPageButtonActive]}
            onPress={() => handleItemsPerPageChange(50)}
          >
            <Text
              style={
                itemsPerPage === 50
                  ? styles.perPageButtonTextActive
                  : styles.perPageButtonText
              }
            >
              50
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B7280',
  },
  //pagination
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  paginationInfo: {
    flex: 1,
  },
  paginationText: {
    fontSize: 14,
    color: '#6B7280',
  },
  paginationControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paginationButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 4,
    marginHorizontal: 2,
  },
  paginationButtonDisabled: {
    borderColor: '#F3F4F6',
    backgroundColor: '#F9FAFB',
  },
  paginationPageInfo: {
    fontSize: 14,
    color: '#4B5563',
    marginHorizontal: 10,
  },
  perPageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
  },
  perPageLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginRight: 8,
  },
  perPageButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 4,
    marginHorizontal: 2,
  },
  perPageButtonActive: {
    backgroundColor: '#4C51BF',
    borderColor: '#4C51BF',
  },
  perPageButtonText: {
    fontSize: 14,
    color: '#4B5563',
  },
  perPageButtonTextActive: {
    fontSize: 14,
    color: '#FFFFFF',
  },
});