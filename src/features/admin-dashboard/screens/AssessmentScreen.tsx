import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Text,
  ActivityIndicator,
} from 'react-native';
import DynamicTable from 'src/features/admin-dashboard/components/dynamic-table/DynamicTable';
import { getGridData, getVisitList } from 'src/screens/login/api';

export default function AssessmentScreen({ navigation }) {
  const [clinician, setClinician] = useState([]);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getGridData({ gridName: 'visitTable' }).then(response => {
      if (response.status === 'ok') {
        setColumns(response.data);

        getVisitList({ limit: 10, page: 1 }).then(response => {
          if (response.status === 'ok') {
            setClinician(response.data);
            setLoading(false);
          }
        });
      }
    });
  }, []);

  // Handle row actions
  const handleEdit = item => {
    console.log('Edit item:', item);
    // Implement your edit logic here
  };

  const handleDelete = id => {
    console.log('Delete item ID:', id);
    // Implement your delete logic here
  };

  const handleDeactivate = id => {
    console.log('Deactivate item ID:', id);
    // Implement your deactivate logic here
  };

  const onSelectRow = clinicianData => {
    navigation.navigate('AdminVisitDetails', {
      clinicianData,
      formConfig: columns,
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4C51BF" />
        <Text style={styles.loadingText}>Loading Visits...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {clinician.length > 0 && (
        <DynamicTable
          data={clinician}
          allColumns={columns}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onDeactivate={handleDeactivate}
          isLoading={false}
          onSelectRow={onSelectRow}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B7280',
  },
});
