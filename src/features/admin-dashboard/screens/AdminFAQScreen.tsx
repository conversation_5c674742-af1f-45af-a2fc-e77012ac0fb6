import React from 'react';
import { ScrollView, View, Text } from 'react-native';

const dataHierarchy = [
  {
    title: 'Client',
    description:
      'A client can have multiple episodes, each covering a specific care period.',
  },
  {
    title: 'Episodes',
    description:
      'Each episode lasts for a 60-day period and consists of multiple visits.',
  },
  {
    title: 'Visits',
    description:
      'Visits are scheduled appointments during an episode for patient care.',
  },
  {
    title: 'Forms',
    description:
      'Each visit contains multiple forms for documentation and care details.',
  },
];

const AdminFAQScreen = () => {
  const DataHierarchyCard = ({ title, description }) => (
    <View style={styles.hierarchyCard}>
      <Text style={styles.hierarchyTitle}>{title}</Text>
      <Text style={styles.hierarchyDescription}>{description}</Text>
    </View>
  );

  return (
    <View style={styles.hierarchyContainer}>
      {dataHierarchy.map((item, index) => (
        <View key={index} style={styles.hierarchyStep}>
          <DataHierarchyCard
            title={item.title}
            description={item.description}
          />
          {index < dataHierarchy.length - 1 && (
            <Text style={styles.arrow}>↓</Text>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = {
  hierarchyContainer: {
    alignItems: 'center',
    padding: 20,
  },
  hierarchyStep: {
    alignItems: 'center',
  },
  hierarchyCard: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 12,
    width: '80%',
    alignItems: 'center',
  },
  hierarchyTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
    color: '#1F2937',
    marginBottom: 8,
  },
  hierarchyDescription: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  arrow: {
    fontSize: 24,
    color: '#9CA3AF',
  },
};

export default AdminFAQScreen;
