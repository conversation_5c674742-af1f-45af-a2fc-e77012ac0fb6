import React, { useCallback, useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Alert,
    ScrollView,
    ActivityIndicator,
    Platform,
} from 'react-native';
import { useResponsive } from 'src/hooks/useResponsive';
// import { Trash, Plus, Edit, ClipboardList } from 'lucide-react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { globalStyles } from 'src/styles';
import { fetchFormList, fetchTemplateList } from '../../form-builder/form-api';
import { Feather, FontAwesome5 } from '@expo/vector-icons';
export const TemplateList = () => {
    const navigation = useNavigation();
    const [loading, setLoading] = useState(true);
    const { isTabletOrMobileDevice } = useResponsive();
    const [hoverItemId, setHoverItemId] = useState<string | null>(null);

    const FORM_LIMIT = 40
    const FORM_PAGE = 1;
    const TEMPLATE_LIMIT = 40;
    const TEMPLATE_PAGE = 1
    const isWeb = Platform.OS === 'web';
    // const [forms, setForms] = useState<FormData[]>([
    //   { id: 'f1', name: 'SOC Assessment', status: 'Active', assessments: 150 },
    //   { id: 'f2', name: 'ROC Form', status: 'Draft', assessments: 0 },
    //   { id: 'f3', name: 'Recertification', status: 'Active', assessments: 10 },
    //   { id: 'f4', name: 'Discharge', status: 'Inactive', assessments: 25 },
    // ]);
    const [forms, setForms] = useState([]);
    const [templates, setTemplates] = useState([]);

    const fetchTemplate = (limit: number, page: number) => {
        setLoading(true);
        fetchTemplateList(limit, page)
            .then((result) => {
                if (result.status == "ok") {
                    setTemplates(result.data)
                }
            })
            .catch((error) => {
                console.log(error);
            }).finally(() => setLoading(false));
    }

    // useEffect(()=>{
    //   fetchList()
    // },[])
    useFocusEffect(
        useCallback(() => {
            fetchTemplate(TEMPLATE_LIMIT, TEMPLATE_PAGE)
        }, [])
    );
    const handleDeleteForm = (id: string) => {
        Alert.alert('Delete Form', 'Are you sure you want to delete this form?', [
            { text: 'Cancel', style: 'cancel' },
            {
                text: 'Delete',
                style: 'destructive',
                onPress: () => {
                    setForms(prev => prev.filter(f => f.id !== id));
                },
            },
        ]);
    };

    const handleFormPress = (id: string) => {
        console.log("id : " + id)
        navigation.navigate('FormCreation', { formId: id, type: "template" });
    };

    const handleStartFromScratch = () => {
        navigation.navigate('FormCreation', { formId: 'new', type: "template" });
    };

    const renderNativeContent = () => {
        return (
            <ScrollView contentContainerStyle={styles.scrollContent}>
                <View
                    style={[
                        styles.gridContainer,
                        isTabletOrMobileDevice && styles.gridContainerMobile,
                    ]}
                >


                    {/* Existing Forms */}
                    {loading &&
                        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                    }
                    {templates.map(form => (
                        <TouchableOpacity
                            key={form._id}
                            style={[
                                styles.formCard,
                                hoverItemId === form._id && styles.cardHover,
                            ]}
                            onPress={() => handleFormPress(form._id)}
                            onMouseEnter={() => setHoverItemId(form._id)}
                            onMouseLeave={() => setHoverItemId(null)}
                        >
                            {/* <View
                style={[
                  styles.badgeContainer,
                  getStatusBadgeStyle(form.status),
                ]}
              >
                <Text style={styles.badgeText}>{form.status}</Text>
              </View> */}
                            <View style={styles.iconContainer}>
                                {/* <ClipboardList size={40} color="#4C51BF" /> */}
                            </View>
                            <Text style={styles.formName}>{form.name}</Text>
                            {/* <View style={styles.countBadge}>
                <Text
                  style={styles.countText}
                >{`${form.assessments} Assessments`}</Text>
              </View> */}

                            <View style={styles.cardActions}>
                                <TouchableOpacity style={styles.actionBtn}>
                                    {/* <Edit size={18} color="#4B5563" /> */}
                                </TouchableOpacity>
                                {/* <TouchableOpacity
                                    style={styles.actionBtn}
                                    onPress={() => handleDeleteForm(form.id)}
                                >
                                    <Trash size={18} color="#EF4444" />
                                </TouchableOpacity> */}
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
        )
    }
    const renderWebContent = () => {
        return (
            <div
                style={{
                    overflowY: 'auto',
                    height: '70vh',
                    paddingTop: '10px'
                }}
            >
                <View
                    style={[
                        styles.gridContainer,
                        isTabletOrMobileDevice && styles.gridContainerMobile,
                    ]}
                >
                    {/* "Start From Scratch" card */}


                    {/* Existing Forms */}
                    {loading &&
                        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                    }
                    {templates.map(form => (
                        <TouchableOpacity
                            key={form._id}
                            style={[
                                styles.formCard,
                                hoverItemId === form._id && styles.cardHover,
                            ]}
                            onPress={() => handleFormPress(form._id)}
                            onMouseEnter={() => setHoverItemId(form._id)}
                            onMouseLeave={() => setHoverItemId(null)}
                        >
                            {/* <View
                style={[
                  styles.badgeContainer,
                  getStatusBadgeStyle(form.status),
                ]}
              >
                <Text style={styles.badgeText}>{form.status}</Text>
              </View> */}
                            <View style={styles.iconContainer}>
                            <FontAwesome5 name="clipboard-list" size={40} color="#4C51BF" />
                                {/* <ClipboardList size={40} color="#4C51BF" /> */}
                            </View>
                            <Text style={styles.formName}>{form.name}</Text>
                            {/* <View style={styles.countBadge}>
                <Text
                  style={styles.countText}
                >{`${form.assessments} Assessments`}</Text>
              </View> */}

                            <View style={styles.cardActions}>
                                <TouchableOpacity style={styles.actionBtn}>
                                <Feather name="edit" size={18} color="#4B5563" />
                                    {/* <Edit size={18} color="#4B5563" /> */}
                                </TouchableOpacity>
                                {/* <TouchableOpacity
                                    style={styles.actionBtn}
                                    onPress={() => handleDeleteForm(form.id)}
                                >
                                    <Trash size={18} color="#EF4444" />
                                </TouchableOpacity> */}
                            </View>
                        </TouchableOpacity>
                    ))}

                </View>
            </div>
        )
    }
    return (
        <View style={styles.root}>
            {/* Header */}
            {/* <Text style={styles.headerTitle}>Forms</Text> */}
            {isWeb ? renderWebContent() : renderNativeContent()}

        </View>
    );
}


const styles = StyleSheet.create({
    root: {
        flex: 1,
        backgroundColor: '#F8FAFC',
    },
    headerTitle: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 22,
        color: '#1F2937',
        marginLeft: 16,
    },
    scrollContent: {
        flexGrow: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    gridContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        width: '80%',
    },
    gridContainerMobile: {
        justifyContent: 'center',
    },
    // FORM CARDS
    formCard: {
        width: 260,
        height: 220,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#E5E7EB',
        borderRadius: 16,
        padding: 16,
        marginRight: 16,
        marginBottom: 16,
        backgroundColor: '#FFFFFF',
        position: 'relative',
    },
    formName: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 16,
        color: '#1F2937',
        marginBottom: 8,
    },
    countBadge: {
        borderRadius: 6,
        paddingHorizontal: 8,
        paddingVertical: 4,
        alignSelf: 'center',
        marginBottom: 12,
        backgroundColor: '#F1F5F9',
    },
    countText: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 12,
    },
    cardActions: {
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 12,
        marginTop: 12,
    },
    actionBtn: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#F9FAFB',
        alignItems: 'center',
        justifyContent: 'center',
    },
    // START FROM SCRATCH CARD
    scratchCard: {
        width: 260,
        height: 220,
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        borderStyle: 'dashed',
        borderWidth: 2,
        borderColor: '#4C51BF',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
        marginBottom: 16,
    },
    scratchText: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 14,
        color: '#4C51BF',
        marginTop: 8,
    },
    // Hover effect
    cardHover: {
        ...globalStyles.shadow,
        transform: [{ scale: 1.01 }],
    },
    badgeContainer: {
        position: 'absolute',
        right: 12,
        top: 12,
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 10,
    },
    badgeText: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 12,
        color: '#111827',
    },
    iconContainer: {
        marginBottom: 12,
    },
});
