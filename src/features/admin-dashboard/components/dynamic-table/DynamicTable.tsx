import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  FlatList,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
// import {
//   Search,
//   Filter,
//   Settings,
//   X,
//   Edit,
//   Trash2,
//   Power,
//   ChevronLeft,
//   ChevronRight,
//   ChevronsLeft,
//   ChevronsRight,
//   PackageOpen,
// } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';

const screenHeight = Dimensions.get('window').height;

const DynamicTable = ({
  data = [],
  allColumns = {},
  onEdit,
  onDelete,
  onDeactivate,
  isLoading = false,
  onSelectRow,
  itemsPerPage = 10
}) => {

  // Transform allColumns from object to array format
  const columnsArray = React.useMemo(() => {
    if (!allColumns || typeof allColumns !== 'object') {
      return [];
    }
    
    return Object.keys(allColumns).map(key => ({
      key,
      label: allColumns[key]?.label || key,
      width: getColumnWidth(key, allColumns[key]),
      questionType: allColumns[key]?.questionType || 'TEXT_INPUT',
      options: allColumns[key]?.options || [],
      isVisible: allColumns[key]?.isVisible !== false, // Default to true if not specified
    }));
  }, [allColumns]);

  // State for visible columns
  const [visibleColumns, setVisibleColumns] = useState([]);

  // Initialize visible columns when columnsArray changes
  useEffect(() => {
    const initialVisibleColumns = columnsArray.filter(item => item.isVisible);
    setVisibleColumns(initialVisibleColumns);
  }, [columnsArray]);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({});
  const [showFilters, setShowFilters] = useState(false);
  const [filteredData, setFilteredData] = useState(data);
  const [showColumnSelector, setShowColumnSelector] = useState(false);

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateField, setDateField] = useState('');

  // Apply search and filters
  useEffect(() => {
    if (!Array.isArray(data)) {
      setFilteredData([]);
      return;
    }

    let result = [...data];

    // Apply search across all visible columns
    if (searchTerm) {
      result = result.filter(item => {
        return visibleColumns.some(column => {
          const key = column.key;
          const value = item[key] ? String(item[key]).toLowerCase() : '';
          return value.includes(searchTerm.toLowerCase());
        });
      });
    }

    // Apply column-specific filters
    Object.keys(filters).forEach(columnKey => {
      const filterValue = filters[columnKey];
      if (filterValue) {
        const column = columnsArray.find(col => col.key === columnKey);
        if (!column) return;

        const dataKey = columnKey;

        switch (column.questionType) {
          case 'DATE_PICKER':
            result = result.filter(item => {
              if (!item[dataKey]) return false;
              return item[dataKey].includes(filterValue);
            });
            break;

          case 'DROPDOWN':
          case 'RADIO':
            result = result.filter(item => {
              if (!item[dataKey]) return false;
              return item[dataKey].toLowerCase() === filterValue.toLowerCase();
            });
            break;

          case 'CHECKBOX':
            const boolValue = filterValue.toLowerCase() === 'true';
            result = result.filter(item => {
              return item[dataKey] === boolValue;
            });
            break;

          default:
            result = result.filter(item => {
              if (!item[dataKey]) return false;
              return String(item[dataKey])
                .toLowerCase()
                .includes(filterValue.toLowerCase());
            });
        }
      }
    });

    setFilteredData(result);
  }, [searchTerm, filters, data, visibleColumns, columnsArray]);

  // Get paginated data
  // const getPaginatedData = () => {
  //   if (!Array.isArray(filteredData)) return [];
    
  //   const startIndex = (currentPage - 1) * itemsPerPage;
  //   const endIndex = startIndex + itemsPerPage;
  //   return filteredData.slice(startIndex, endIndex);
  // };

  // Determine column width based on column type
  function getColumnWidth(key, columnInfo) {
    if (!columnInfo) return 150;
    
    switch (columnInfo.questionType) {
      case 'DATE_PICKER':
        return 120;
      case 'CHECKBOX':
        return 100;
      case 'RADIO':
        return 100;
      case 'TEXT_INPUT':
        return key === 'address1' || key === 'address2' ? 200 : 150;
      default:
        return 150;
    }
  }

  // Toggle column visibility
  const toggleColumn = columnKey => {
    if (visibleColumns.find(col => col.key === columnKey)) {
      setVisibleColumns(visibleColumns.filter(col => col.key !== columnKey));
    } else {
      const columnToAdd = columnsArray.find(col => col.key === columnKey);
      if (columnToAdd) {
        setVisibleColumns([...visibleColumns, columnToAdd]);
      }
    }
  };

  // Handle filter change
  const handleFilterChange = (key, value) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  // Handle date filter
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split('T')[0];
      handleFilterChange(dateField, dateString);
    }
  };

  // Open date picker for a specific field
  const openDatePicker = fieldKey => {
    setDateField(fieldKey);
    setShowDatePicker(true);
  };

  // Render column header
  const renderHeader = () => (
    <View style={styles.headerRow}>
      {visibleColumns.map(column => (
        <View
          key={column.key}
          style={[styles.headerCell, { width: column.width || 150 }]}
        >
          <Text style={styles.headerText}>{column.label}</Text>
          <TouchableOpacity
            onPress={() => handleFilterChange(column.key, '')}
            style={styles.filterIcon}
          >
            {/* <Filter
              size={16}
              color={filters[column.key] ? '#4C51BF' : '#9CA3AF'}
            /> */}
          </TouchableOpacity>
        </View>
      ))}
      <View style={[styles.headerCellAction, { width: 120 }]}>
        <Text style={styles.headerText}>Actions</Text>
      </View>
    </View>
  );

  // Render table row
  const renderRow = ({ item, index }) => {
    // Ensure item exists and has required properties
    if (!item || typeof item !== 'object') {
      return null;
    }

    return (
      <TouchableOpacity 
        style={styles.row} 
        onPress={() => onSelectRow && onSelectRow(item)}
      >
        {visibleColumns.map(column => {
          const dataKey = column.key;
          const cellValue = item[dataKey];
          
          return (
            <View
              key={`${column.key}-${index}`}
              style={[styles.cell, { width: column.width || 150 }]}
            >
              <Text style={styles.cellText}>
                {cellValue !== undefined && cellValue !== null ? String(cellValue) : '-'}
              </Text>
            </View>
          );
        })}
        <View style={[styles.cellAction, { width: 120 }]}>
          <View style={styles.actionBtnContainer}>
            {onEdit && (
              <TouchableOpacity
                style={[styles.actionButton, styles.editButton]}
                onPress={() => onEdit(item)}
              >
                {/* <Edit size={16} color="#FFFFFF" /> */}
              </TouchableOpacity>
            )}
            {onDelete && (
              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={() => onDelete(item._id || item.id)}
              >
                {/* <Trash2 size={16} color="#FFFFFF" /> */}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Column selector modal
  const renderColumnSelector = () => (
    <Modal
      visible={showColumnSelector}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowColumnSelector(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Select Columns</Text>
          <TouchableOpacity 
            style={styles.closeIcon} 
            onPress={() => setShowColumnSelector(false)}
          >
            {/* <X size={20} color="#4B5563" /> */}
          </TouchableOpacity>
          <ScrollView style={styles.columnList}>
            {columnsArray.map(column => (
              <TouchableOpacity
                key={column.key}
                style={styles.columnOption}
                onPress={() => toggleColumn(column.key)}
              >
                <View style={styles.checkboxContainer}>
                  <View
                    style={[
                      styles.checkbox,
                      visibleColumns.find(col => col.key === column.key)
                        ? styles.checkboxChecked
                        : {},
                    ]}
                  >
                    {visibleColumns.find(col => col.key === column.key) && (
                      <Text style={styles.checkmark}>✓</Text>
                    )}
                  </View>
                </View>
                <Text style={styles.columnOptionText}>{column.label}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowColumnSelector(false)}
            >
              <LinearGradient
                colors={['#4C51BF', '#6B46C1']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.buttonGradient}
              >
                <Text style={styles.closeButtonText}>Apply</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Render filter input based on question type
  const renderFilterInput = column => {
    switch (column.questionType) {
      case 'DROPDOWN':
        return (
          <View style={styles.filterDropdown}>
            <TextInput
              style={styles.filterInput}
              value={filters[column.key] || ''}
              onChangeText={text => handleFilterChange(column.key, text)}
              placeholder={`Filter by ${column.label.toLowerCase()}`}
              placeholderTextColor="#9CA3AF"
            />
          </View>
        );

      case 'CHECKBOX':
        return (
          <View style={styles.radioContainer}>
            <TouchableOpacity
              style={[
                styles.radioOption,
                filters[column.key] === 'true' && styles.radioSelected,
              ]}
              onPress={() => handleFilterChange(column.key, 'true')}
            >
              <Text style={styles.radioText}>Yes</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.radioOption,
                filters[column.key] === 'false' && styles.radioSelected,
              ]}
              onPress={() => handleFilterChange(column.key, 'false')}
            >
              <Text style={styles.radioText}>No</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.radioOption,
                (!filters[column.key] || filters[column.key] === '') &&
                  styles.radioSelected,
              ]}
              onPress={() => handleFilterChange(column.key, '')}
            >
              <Text style={styles.radioText}>Any</Text>
            </TouchableOpacity>
          </View>
        );

      case 'RADIO':
        return (
          <View style={styles.radioContainer}>
            {column.options &&
              column.options.map(option => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.radioOption,
                    filters[column.key] === option && styles.radioSelected,
                  ]}
                  onPress={() => handleFilterChange(column.key, option)}
                >
                  <Text
                    style={[
                      styles.radioText,
                      filters[column.key] === option &&
                        styles.radioTextSelected,
                    ]}
                  >
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            <TouchableOpacity
              style={[
                styles.radioOption,
                (!filters[column.key] || filters[column.key] === '') &&
                  styles.radioSelected,
              ]}
              onPress={() => handleFilterChange(column.key, '')}
            >
              <Text
                style={[
                  styles.radioText,
                  (!filters[column.key] || filters[column.key] === '') &&
                    styles.radioTextSelected,
                ]}
              >
                Any
              </Text>
            </TouchableOpacity>
          </View>
        );

      case 'DATE_PICKER':
        return (
          <TouchableOpacity
            style={styles.datePickerButton}
            onPress={() => openDatePicker(column.key)}
          >
            <Text style={styles.datePickerText}>
              {filters[column.key] || 'Select date...'}
            </Text>
          </TouchableOpacity>
        );

      default:
        return (
          <TextInput
            style={styles.filterInput}
            value={filters[column.key] || ''}
            onChangeText={text => handleFilterChange(column.key, text)}
            placeholder={`Filter by ${column.label.toLowerCase()}`}
            placeholderTextColor="#9CA3AF"
          />
        );
    }
  };

  // Filter panel
  const renderFilterPanel = () => (
    <Modal
      visible={showFilters}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowFilters(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Filter Data</Text>
          <TouchableOpacity 
            style={styles.closeIcon} 
            onPress={() => setShowFilters(false)}
          >
            {/* <X size={20} color="#4B5563" /> */}
          </TouchableOpacity>
          <ScrollView style={styles.filterList}>
            {visibleColumns.map(column => (
              <View key={column.key} style={styles.filterItem}>
                <Text style={styles.filterLabel}>{column.label}</Text>
                {renderFilterInput(column)}
              </View>
            ))}
          </ScrollView>
          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setFilters({})}
            >
              <Text style={styles.clearButtonText}>Clear All</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowFilters(false)}
            >
              <LinearGradient
                colors={['#4C51BF', '#6B46C1']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.buttonGradient}
              >
                <Text style={styles.closeButtonText}>Apply Filters</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Show loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4C51BF" />
        <Text style={styles.loadingText}>Loading data...</Text>
      </View>
    );
  }

  // Show empty state if no columns are configured
  if (columnsArray.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        {/* <PackageOpen size={100} color="#6B7280" strokeWidth={1} /> */}
        <Text style={styles.loadingText}>No table configuration found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Search and toolbar area */}
      <View style={styles.toolbar}>
        <View style={styles.searchContainer}>
          <View style={styles.searchIconWrapper}>
            {/* <Search color="#9CA3AF" size={20} /> */}
          </View>
          <TextInput
            style={styles.searchInput}
            placeholder="Search across columns..."
            placeholderTextColor="#9CA3AF"
            value={searchTerm}
            onChangeText={setSearchTerm}
          />
        </View>
        <View style={styles.toolbarButtons}>
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={() => setShowFilters(true)}
          >
            {/* <Filter size={16} color="#4B5563" /> */}
            <Text style={styles.toolbarButtonText}>Filters</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.toolbarButton}
            onPress={() => setShowColumnSelector(true)}
          >
            {/* <Settings size={16} color="#4B5563" /> */}
            <Text style={styles.toolbarButtonText}>Columns</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Table container */}
      <View style={styles.tableContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View>
            {renderHeader()}
            {filteredData.length === 0 ? (
              <View style={styles.emptyContainer}>
                {/* <PackageOpen size={60} color="#6B7280" strokeWidth={1} /> */}
                <Text style={styles.emptyText}>
                  No data found for selected filter
                </Text>
              </View>
            ) : (
              <FlatList
                data={filteredData}
                renderItem={renderRow}
                keyExtractor={(item, index) => item._id || item.id || index.toString()}
                scrollEnabled={true}
                initialNumToRender={itemsPerPage}
                maxToRenderPerBatch={itemsPerPage}
                windowSize={10}
                removeClippedSubviews={true}
                contentContainerStyle={{ 
                  minHeight: Math.max(200, screenHeight - 400)
                }}
              />
            )}
          </View>
        </ScrollView>
      </View>

      {/* Modals */}
      {renderColumnSelector()}
      {renderFilterPanel()}

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
        />
      )}
    </View>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B7280',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  toolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e7ff',
  },
  searchContainer: {
    flex: 1,
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  searchIconWrapper: {
    paddingLeft: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    paddingLeft: 8,
    paddingRight: 16,
    fontSize: 14,
    color: '#1F2937',
    height: 48,
  },
  toolbarButtons: {
    flexDirection: 'row',
    marginLeft: 12,
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 12,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    height: 48,
  },
  toolbarButtonText: {
    fontSize: 14,
    marginLeft: 5,
    color: '#4B5563',
  },
  tableContainer: {
    flex: 1,
    borderRadius: 12,
    overflow: 'auto',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
  },
  headerRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  headerCell: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
  },
  headerCellAction: {
    padding: 12,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4B5563',
  },
  filterIcon: {
    padding: 4,
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    minHeight: 50,
  },
  cell: {
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
  },
  cellAction: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  cellText: {
    fontSize: 14,
    color: '#4B5563',
  },
  actionBtnContainer: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 2,
  },
  editButton: {
    backgroundColor: '#3B82F6',
  },
  deleteButton: {
    backgroundColor: '#EF4444',
  },
  deactivateButton: {
    backgroundColor: '#F59E0B',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    maxHeight: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 20,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  closeIcon: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 4,
  },
  columnList: {
    maxHeight: 400,
  },
  columnOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  checkboxContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkbox: {
    width: 18,
    height: 18,
    borderWidth: 2,
    borderColor: '#6B7280',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#4C51BF',
    borderColor: '#4C51BF',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  columnOptionText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 10,
  },
  filterList: {
    maxHeight: 400,
  },
  filterItem: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4B5563',
    marginBottom: 6,
  },
  filterInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 12,
    fontSize: 14,
    color: '#4B5563',
  },
  radioContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  radioOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  radioSelected: {
    backgroundColor: '#4C51BF',
    borderColor: '#4C51BF',
  },
  radioText: {
    fontSize: 14,
    color: '#4B5563',
  },
  radioTextSelected: {
    color: '#FFFFFF',
  },
  filterDropdown: {
    height: 40,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
  },
  datePickerButton: {
    height: 40,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  datePickerText: {
    fontSize: 14,
    color: '#4B5563',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  clearButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 6,
  },
  clearButtonText: {
    fontSize: 14,
    color: '#4B5563',
  },
  closeButton: {
    borderRadius: 6,
    overflow: 'hidden',
  },
  buttonGradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  closeButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
};

export default DynamicTable;