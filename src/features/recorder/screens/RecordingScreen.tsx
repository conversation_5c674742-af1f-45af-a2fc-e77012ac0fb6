import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  FlatList,
  Platform,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
  useSharedValue,
  interpolate,
} from 'react-native-reanimated';
// import {
//   Mic,
//   Pause,
//   Square,
//   Play,
//   WifiOff,
//   CheckSquare,
//   List,
// } from 'lucide-react-native';
import { Audio } from 'expo-av';
import NetInfo from '@react-native-community/netinfo';
import { LinearGradient } from 'expo-linear-gradient';
import MobileHeader from 'src/components/mobile-header/MobileHeader';
import { useNavigation } from '@react-navigation/native';
import theme from 'src/theme';

const questionsData = [
  { section: 'Section A', questions: ['Question 1', 'Question 2'] },
  { section: 'Section B', questions: ['Question 3', 'Question 4'] },
];

const RecordingScreen = ({ route }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isOffline, setIsOffline] = useState(false);
  const [questionsVisible, setQuestionsVisible] = useState(false);
  const [checkedQuestions, setCheckedQuestions] = useState({});
  const [recording, setRecording] = useState(null);
  const [recordingURI, setRecordingURI] = useState(null);
  const [sound, setSound] = useState(null);
  const volumeLevel = useSharedValue(0);

  const navigation = useNavigation();

  const { visit } = route.params;

  const onGoBack = () => {
    navigation.goBack();
  };

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOffline(!state.isConnected);
    });

    if (isRecording) {
      const interval = setInterval(() => {
        setRecordingTime(prev => prev + 1);
        volumeLevel.value = Math.random();
      }, 1000);

      const silenceTimeout = setTimeout(() => {
        Alert.alert('No Audio Detected', 'No audio captured for 10 minutes.');
      }, 600000);

      return () => {
        clearInterval(interval);
        clearTimeout(silenceTimeout);
      };
    }

    return () => unsubscribe();
  }, [isRecording]);

  const volumeStyle = useAnimatedStyle(() => ({
    height: interpolate(volumeLevel.value, [0, 1], [20, 100]),
    backgroundColor: '#6C5CE7',
    marginVertical: 5,
    borderRadius: 10,
  }));

  const toggleQuestionCheck = (section, question) => {
    setCheckedQuestions(prev => ({
      ...prev,
      [`${section}-${question}`]: !prev[`${section}-${question}`],
    }));
  };

  const startRecording = async () => {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({ allowsRecordingIOS: true });
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY,
      );
      setRecording(recording);
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording', error);
    }
  };

  const stopRecording = async () => {
    setIsRecording(false);
    await recording.stopAndUnloadAsync();
    const uri = recording.getURI();
    setRecordingURI(uri);
    console.log('Recording saved to', uri);
  };

  const playRecording = async () => {
    try {
      if (!recordingURI) return;
      const { sound } = await Audio.Sound.createAsync({ uri: recordingURI });
      setSound(sound);
      await sound.playAsync();
    } catch (error) {
      console.error('Failed to play recording', error);
    }
  };

  return (
    <View style={styles.container}>
      <MobileHeader title={'Recording'} onBack={onGoBack} />
      <View style={styles.statusContainer}>
        {/* <WifiOff size={24} color={isOffline ? 'red' : 'gray'} /> */}
        <Text style={styles.statusText}>
          {isOffline ? 'Offline' : 'Online'}
        </Text>
      </View>

      <View style={styles.recordingContainer}>
        <Animated.View style={[styles.volumeBar, volumeStyle]} />

        <Text
          style={styles.timer}
        >{`${Math.floor(recordingTime / 60)}:${recordingTime % 60}`}</Text>

        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={isRecording ? stopRecording : startRecording}
          >
            {/* {isRecording ? (
              <Pause size={24} color="#6C5CE7" />
            ) : (
              <Mic size={24} color="#6C5CE7" />
            )} */}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={playRecording}
          >
            {/* <Play size={24} color="#6C5CE7" /> */}
          </TouchableOpacity>
        </View>
      </View>

      <TouchableOpacity
        style={styles.buttonContainer}
        activeOpacity={0.9}
        onPress={() => setQuestionsVisible(!questionsVisible)}
      >
        <LinearGradient
          colors={['#4C51BF', '#6B46C1']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.buttonGradient}
        >
          <View style={styles.buttonContent}>
            {/* <List color="#FFFFFF" size={20} /> */}
            <Text style={styles.buttonText}>View Questions</Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>

      {questionsVisible && (
        <FlatList
          data={questionsData}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item }) => (
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>{item.section}</Text>
              {item.questions.map((question, idx) => (
                <TouchableOpacity
                  key={idx}
                  style={styles.questionItem}
                  onPress={() => toggleQuestionCheck(item.section, question)}
                >
                  {/* <CheckSquare
                    size={20}
                    color={
                      checkedQuestions[`${item.section}-${question}`]
                        ? '#6C5CE7'
                        : 'gray'
                    }
                  /> */}
                  <Text style={styles.questionText}>{question}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#333',
  },
  recordingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  volumeBar: {
    width: 50,
    backgroundColor: '#6C5CE7',
  },
  timer: {
    fontSize: 24,
    color: '#1a1a1a',
    marginVertical: 20,
  },
  controls: {
    flexDirection: 'row',
    gap: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F0EEFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionsButton: {
    backgroundColor: '#6C5CE7',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
  },
  questionsText: {
    color: '#fff',
    fontSize: 16,
  },
  sectionContainer: {
    marginVertical: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  questionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  questionText: {
    marginLeft: 10,
    fontSize: 16,
  },
  buttonContainer: {
    height: 56,
  },
  buttonGradient: {
    borderRadius: 12,
    paddingVertical: 12,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
  },
  buttonText: {
    marginLeft: 8,
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
});

export default RecordingScreen;
