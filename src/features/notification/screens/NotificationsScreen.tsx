import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
// import { UserCheck, FileText } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { globalStyles } from 'src/styles';
import theme from 'src/theme';
import { fetchNotification } from './api';

import { formatDateDifference } from '@/src/utils/DateUtils';
import { SafeAreaView } from 'react-native-safe-area-context';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const TABS = ['All', 'Assigned Visits', 'AI Transcript'];

const NotificationsScreen = () => {
  const [activeTab, setActiveTab] = useState('All');
  const [notifications, setNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  setPageTitle('Notifications');
  useEffect(() => {
    const fetchedNotifications = [
      {
        id: '1',
        type: 'audio',
        title: 'AI Transcript Ready for Start of Care',
        description:
          'The AI-generated transcript for the Start of Care visit is now available. Please review the details to ensure accuracy.',
        time: 'Today, 9:30 AM',
        isRead: false,
      },
      {
        id: '2',
        type: 'visit',
        title: 'Visit Assigned for Start of Care',
        description:
          'Patient: John Doe | Date: February 15, 2025 | The initial Start of Care visit has been scheduled. Please review the details and confirm availability.',
        time: 'Today, 8:45 AM',
        isRead: false,
      },
      {
        id: '3',
        type: 'audio',
        title: 'AI Transcript Ready for Start of Care',
        description:
          'The Start of Care visit transcript has been processed. Ensure to validate and make necessary corrections before submission.',
        time: 'Yesterday, 3:15 PM',
        isRead: true,
      },
      {
        id: '4',
        type: 'visit',
        title: 'Visit Assigned for Start of Care',
        description:
          'Patient: Jane Smith | Date: February 16, 2025 | A new Start of Care visit has been assigned. Please check patient history before the visit.',
        time: 'Yesterday, 10:00 AM',
        isRead: false,
      },
      {
        id: '5',
        type: 'audio',
        title: 'AI Transcript Ready for Start of Care',
        description:
          'AI has completed the transcription for your Start of Care visit. Please review and finalize the documentation.',
        time: '2 Days Ago, 4:20 PM',
        isRead: true,
      },
      {
        id: '6',
        type: 'visit',
        title: 'Visit Assigned for Start of Care',
        description:
          'Patient: Michael Brown | Date: February 17, 2025 | A Start of Care visit is scheduled. Ensure necessary documentation is prepared.',
        time: '2 Days Ago, 11:10 AM',
        isRead: false,
      },
      {
        id: '7',
        type: 'audio',
        title: 'AI Transcript Ready for Start of Care',
        description:
          'Start of Care transcription is now available. Review and update any missing details before proceeding.',
        time: '3 Days Ago, 5:30 PM',
        isRead: true,
      },
      {
        id: '8',
        type: 'visit',
        title: 'Visit Assigned for Start of Care',
        description:
          'Patient: Sarah Lee | Date: February 18, 2025 | The Start of Care visit is confirmed. Please verify all patient information beforehand.',
        time: '3 Days Ago, 12:15 PM',
        isRead: true,
      },
      {
        id: '9',
        type: 'audio',
        title: 'AI Transcript Ready for Start of Care',
        description:
          'A new Start of Care transcript is ready for review. Please ensure accuracy before approval.',
        time: '4 Days Ago, 6:00 PM',
        isRead: false,
      },
      {
        id: '10',
        type: 'visit',
        title: 'Visit Assigned for Start of Care',
        description:
          "Patient: David Johnson | Date: February 19, 2025 | The Start of Care visit has been scheduled. Check the patient's medical records in advance.",
        time: '4 Days Ago, 9:00 AM',
        isRead: true,
      },
    ];
    setLoading(true);
      fetchNotification()
        .then((result) => {
          // console.log(result.data);
          // setVisits(result.data);
          setNotifications(result.data);
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => setLoading(false));
    // setNotifications(fetchedNotifications);
  }, []);

  useEffect(() => {
    let filteredData;
    if (activeTab === 'All') {
      filteredData = notifications;
    } else if (activeTab === 'Assigned Visits') {
      filteredData = notifications.filter(n => n.type === 'visit');
    } else {
      filteredData = notifications.filter(n => n.type === 'audio');
    }
    setFilteredNotifications(filteredData);
  }, [activeTab, notifications]);

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.header}>Notifications</Text>

      {/* <View style={styles.tabContainer}>
        {TABS.map(tab => (
          <TouchableOpacity
            key={tab}
            style={[styles.tag, activeTab === tab && styles.selectedTag]}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              style={[
                styles.tagText,
                activeTab === tab && styles.selectedTagText,
              ]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View> */}
    {loading ? (
      <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
    ) : notifications.length > 0 ? (
      <FlatList
        style={{ paddingHorizontal: 16 }}
        data={filteredNotifications}
        keyExtractor={item => item._id}
        renderItem={({ item }) => (
          
           <View
            style={[
              styles.notificationCard,
              !item?.isRead ? styles.unRead : {},
            ]}
            
          >
            {!item.isRead ? (
              <View style={styles.indicator} />
            ) : (
              <View style={{ width: 10 }} />
            )}
            
            <MaterialCommunityIcons
              name="account-check-outline"
              size={20}
              color={theme.colors.secondary}
            />
            <View style={styles.notificationText}>
              <Text
                style={styles.notificationDesc}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {item.notificationContent}
              </Text>
              <Text style={styles.notificationTime}>{formatDateDifference(item.createdAt)}</Text>
            </View>
          </View>
          
        )}
      />
    ) : (
      <Text style={{ textAlign: "center", marginTop: 20 }}>No New Notification available</Text>
    )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: '#fff',
    paddingVertical: 20,
  },
  header: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 20,
    color: theme.colors.text,
    paddingHorizontal: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    marginVertical: 16,
    gap: 8,
    paddingHorizontal: 16,
  },
  tag: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.colors.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    height: 36,
  },
  tagText: {
    color: theme.colors.secondary,
    fontSize: 13,
    fontFamily: 'Poppins_400Regular',
  },
  selectedTag: {
    backgroundColor: theme.colors.secondary,
  },
  selectedTagText: {
    color: '#fff',
  },
  notificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 12,
    borderRadius: 10,
    backgroundColor: '#F9FAFB',
  },
  notificationText: {
    marginLeft: 12,
    flex: 1,
  },
  notificationTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    color: theme.colors.text,
  },
  notificationDesc: {
    fontSize: 13,
    color: '#6B7280',
  },
  notificationTime: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
  },
  unRead: {
    backgroundColor: '#EFF6FF',
  },
  indicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
    backgroundColor: '#f26649',
  },
});

export default NotificationsScreen;
