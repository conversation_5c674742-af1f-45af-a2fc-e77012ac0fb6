import React from 'react';
import { View, Text, Dimensions, StyleSheet } from 'react-native';
import Svg, { Circle, G } from 'react-native-svg';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '@/src/theme';

const CIRCLE_SIZE = 100; // Fixed size for the circle
const STROKE_WIDTH = 12;
const RADIUS = (CIRCLE_SIZE - STROKE_WIDTH) / 2;
const CIRCUMFERENCE = 2 * Math.PI * RADIUS;

const DailyActivityReport = ({ todaysVisit }) => {

  const calulatePercentage = (total: number, value: number) => {
    if (total != 0 && value != 0) {
      return ((value / total) * 100);
    } else {
      return 0;
    }
  }
  const calculateArc = percentage => {
    const strokeDasharray = (percentage * CIRCUMFERENCE) / 100;
    return `${strokeDasharray} ${CIRCUMFERENCE}`;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Today Report</Text>

      <LinearGradient colors={['#ffffff', '#ffffff']} style={styles.card}>
        <View style={styles.contentContainer}>
          {/* Circle Progress */}
          <View style={styles.progressContainer}>
            <Svg
              width={CIRCLE_SIZE}
              height={CIRCLE_SIZE}
              style={{ transform: [{ rotate: '-90deg' }] }}
            >
              {/* Walk - 40% (yellow) - Starts at 0 degrees */}
              <Circle
                cx={CIRCLE_SIZE / 2}
                cy={CIRCLE_SIZE / 2}
                r={RADIUS}
                fill="none"
                stroke="#FFB84C"
                strokeWidth={STROKE_WIDTH}
                strokeDasharray={calculateArc(calulatePercentage(todaysVisit.totalVisits,todaysVisit.inProgressVisits))}
                strokeLinecap="round"
              />

              {/* Rest - 20% (coral) - Starts after Walk (144 degrees = 40% of 360) */}
              <G
                rotation={144}
                origin={`${CIRCLE_SIZE / 2}, ${CIRCLE_SIZE / 2}`}
              >
                <Circle
                  cx={CIRCLE_SIZE / 2}
                  cy={CIRCLE_SIZE / 2}
                  r={RADIUS}
                  fill="none"
                  stroke="#FF6B6B"
                  strokeWidth={STROKE_WIDTH}
                  strokeDasharray={calculateArc(calulatePercentage(todaysVisit.totalVisits,todaysVisit.newVisits))}
                  strokeLinecap="round"
                />
              </G>

              {/* Sit - 40% (mint) - Starts after Rest (216 degrees = 60% of 360) */}
              <G
                rotation={216}
                origin={`${CIRCLE_SIZE / 2}, ${CIRCLE_SIZE / 2}`}
              >
                <Circle
                  cx={CIRCLE_SIZE / 2}
                  cy={CIRCLE_SIZE / 2}
                  r={RADIUS}
                  fill="none"
                  stroke="#4ECDC4"
                  strokeWidth={STROKE_WIDTH}
                  strokeDasharray={calculateArc(calulatePercentage(todaysVisit.totalVisits,todaysVisit.completedVisits))}
                  strokeLinecap="round"
                />
              </G>
            </Svg>

            {/* Center Text */}
            <View style={styles.centerText}>
              <Text style={styles.dayText}>5</Text>
              <Text style={styles.monthText}>Visits</Text>
            </View>
          </View>

          {/* Legend */}
          <View style={styles.legendContainer}>
            <View style={styles.legendItems}>
              <View style={styles.legendItem}>
                <View style={[styles.dot, { backgroundColor: '#FFB84C' }]} />
                <Text style={styles.legendText}>In Progress:</Text>
                <Text style={styles.count}> {todaysVisit.inProgressVisits} </Text>
              </View>

              <View style={styles.legendItem}>
                <View style={[styles.dot, { backgroundColor: '#FF6B6B' }]} />
                <Text style={styles.legendText}>Not Started:</Text>
                <Text style={styles.count}> {todaysVisit.newVisits} </Text>
              </View>

              <View style={styles.legendItem}>
                <View style={[styles.dot, { backgroundColor: '#4ECDC4' }]} />
                <Text style={styles.legendText}>Completed:</Text>
                <Text style={styles.count}> {todaysVisit.completedVisits} </Text>
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8FAFC',
    paddingHorizontal: 20,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#1E293B',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginTop: 10,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 32,
  },
  progressContainer: {
    position: 'relative',
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerText: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayText: {
    fontSize: 24,
    color: '#334155',
    fontFamily: 'Poppins_600SemiBold',
  },
  monthText: {
    fontSize: 12,
    color: '#64748B',
    fontFamily: 'Poppins_400Regular',
  },
  legendContainer: {
    flex: 1,
  },
  legendTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#334155',
    marginBottom: 12,
    fontFamily: 'Poppins_600SemiBold',
  },
  legendItems: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 13,
    color: theme.colors.ternaryText,
    fontFamily: 'Poppins_400Regular',
  },
  count: {
    fontSize: 15,
    color: theme.colors.text,
    fontFamily: 'Poppins_500Medium',
  },
});

export default DailyActivityReport;
