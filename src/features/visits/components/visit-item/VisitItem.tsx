
import { Dimensions, StyleSheet, Text, TouchableOpacity, View,Modal,Image, ActivityIndicator } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
// import { Clock, MapPin } from "lucide-react-native";
import React, { use, useCallback, useEffect, useState } from "react";
import { globalStyles } from "src/styles";
import { FontAwesome, Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

import theme from "@/src/theme";
import { changeVisitStatus } from "../upcoming-visit/visit-api";
import { DynamicMessageModalWithConfirm, MessageModal } from "@/src/utils/MessageModal";
import { UPCOMING_CLOCK_BLACK, UPCOMING_CLOCK_WHITE } from "@/assets/images";
const THEME_COLOR = '#007AFF';
const { width } = Dimensions.get('window');
import { Feather } from '@expo/vector-icons';
import { MISSED_VISIT_FAILED, MISSED_VISIT_SUCCESS } from "@/src/components/messages";


export const MissedVisitModal = ({ visible, onCancel,onConfirm ,isLoading}) => {
  // let values = [
  //   { value: "Patient No-Show", label: "Patient No-Show" },
  //   { value: "Patient Canceled", label: "Patient Canceled" },
  //   { value: "Clinician Canceled", label: "Clinician Canceled" },
  //   { value: "Weather / Emergency", label: "Weather / Emergency" },
  //   { value: "Others", label: "Others" }
  // ];

  // const [selectedRadio, setSelectedRadio] = useState("");
  // const [showTextBox, setShowTextBox] = useState(false);
  // const [textBoxValue, setTextBoxValue] = useState("");

  // const handleRadioSelect = (value) => {
  //   setSelectedRadio(value);
  //   // console.log(value)
  //   // Show text box only when "Patient No-Show" is selected
  //   if (value === "Others") {
  //     setShowTextBox(true);
  //   } else {
  //     setShowTextBox(false);
  //     setTextBoxValue(""); // Clear text when other options are selected
  //   }
  // };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={deleteRecordStyle.overlay}>
        <View style={deleteRecordStyle.modalContainer}>
          <View style={deleteRecordStyle.iconContainer}>
            <View style={deleteRecordStyle.icon}>
              <Ionicons name="checkmark-circle" size={35} color="#1D75F5" />
            </View>
          </View>
          <Text style={deleteRecordStyle.title}>Missed Visit?</Text>
          <Text style={deleteRecordStyle.message}>
            Do you want to mark the visit as missed?
          </Text>
          <View style={deleteRecordStyle.buttonContainer}>
            <TouchableOpacity style={deleteRecordStyle.cancelButton} onPress={onCancel}>
              <Text style={deleteRecordStyle.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={deleteRecordStyle.confirmButton} onPress={onConfirm}>
              <View style={deleteRecordStyle.buttonContent}>
                                    {isLoading ? (
                                      <ActivityIndicator
                                        size={"small" }
                                        color="#FFFFFF"
                                      />
                                    ) : (
                                      <Text style={deleteRecordStyle.confirmButtonText}>Confirm</Text>
                                      //<Text allowFontScaling={false} id="login-sign-in-button" style={styles.buttonText}>Sign In</Text>
                                    )}
                                  </View>
              
            </TouchableOpacity>
          </View>
          {/* </View> */}
          {/* <Text style={deleteRecordStyle.title}>Select Reason</Text>
          
          {values.map(radioItem => (
            <TouchableOpacity
              key={radioItem.value}
              style={deleteRecordStyle.radioRow}
              onPress={() => handleRadioSelect(radioItem.value)}
            >
              <View
                style={[
                  deleteRecordStyle.radioOuter,
                  selectedRadio === radioItem.value && deleteRecordStyle.radioOuterSelected,
                ]}
              >
                {selectedRadio === radioItem.value && (
                  <View style={deleteRecordStyle.radioInner} />
                )}
              </View>
              <Text style={deleteRecordStyle.labelText}>{radioItem.label}</Text>
            </TouchableOpacity>
          ))}
          
          {showTextBox && (
            <View style={deleteRecordStyle.textBoxContainer}>
              <Text style={deleteRecordStyle.textBoxLabel}>Please provide additional details:</Text>
              
              <input
                style={deleteRecordStyle.textBox}
                placeholder="Enter details here..."
                value={textBoxValue}
                onChange={(e) => setTextBoxValue(e.target.value)}
                type="text"
              />
            </View>
          )} */}

          {/* <View style={deleteRecordStyle.buttonContainer}>
            <TouchableOpacity style={deleteRecordStyle.cancelButton} onPress={onCancel}>
              <Text style={deleteRecordStyle.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={deleteRecordStyle.confirmButton} onPress={onCancel}>
              <Text style={deleteRecordStyle.confirmButtonText}>Submit</Text>
            </TouchableOpacity>
          </View> */}
        </View>
      </View>
    </Modal>
  );
};

const deleteRecordStyle = StyleSheet.create({
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center', // Changed from 'left' to 'center'
    alignSelf: 'flex-start', // Added this to align the entire row to the left
    marginBottom: 8,

    width: '100%', // Added to ensure full width
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: THEME_COLOR, // Make sure THEME_COLOR is defined in your app
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioOuterSelected: {
    borderColor: THEME_COLOR, // Make sure THEME_COLOR is defined in your app
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: THEME_COLOR, // Make sure THEME_COLOR is defined in your app
  },
  labelText: {
    fontSize: 16,
    color: '#000',
    flex: 1, // Added to allow text to take remaining space
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.95,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center', // This centers the modal content
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 25,
  },
  title: {
    color: '#000',
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    fontStyle: 'normal',
    marginBottom: 10,
  },
  message: {
    color: '#7C7887',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: '400',
    marginTop: 10,
    marginBottom: 15
  },
  icon: {
    width: 60,
    height: 60,
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '75%',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height:  24 ,
    },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  textBoxContainer: {
    width: '100%',
    marginTop: 15,
    marginBottom: 10,
  },
  textBoxLabel: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    fontFamily: 'Poppins_500Medium',
  },
  textBox: {
    width: '100%',
    height: 80,
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    backgroundColor: '#FFF',
  },
});

const VisitItem = ({
  visit,
  onClickVisit,
  type,
  onRefresh
}: {
  visit: any;
  onClickVisit: (visit: any) => void;
  type: string;
  onRefresh: (visitId: string) => void;
}) => {
  //console.log("Visit:" + JSON.stringify(visit))
  const [checkValue, setCheckValue] = useState(false);
  const [isVisible, setIsvisible] = useState(false)

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success");
  const [isLoading, setIsLoading] = useState(false)
  const [isOpenMessageModal, setIsOpenMessageModal] = useState(false);

  const navigation = useNavigation();
  const visitAddress = visit?.clientAddress2 ? `${visit?.clientAddress1} , ${visit?.clientAddress2}` :`${visit?.clientAddress1}`
  const getBorderColor = (type) => {
    switch (type) {
      case "New":
        return {
          backgroundColor: "#44AFC7",
        };
      case "To be reviewed":
        return {
          backgroundColor: "#F9A704",
        };
      case "Past Due":
        return {
          backgroundColor: "#F26649",
        };
      default:
        return {
          backgroundColor: "#0079FE",
        };
    }
  };
  const onsubmit = () => {
    setIsLoading(true)
    // console.log("onSubmit called")

    
    changeVisitStatus({ visitId: visit._id, status: "Missed" })
      .then((result) => {
        if (result.status == "ok") {
          // if (onRefresh) {
          //   onRefresh(visit._id)
          // }
           setIsOpenMessageModal(true)

        } else {
          setShowMessageModal(true)
          setMessage(MISSED_VISIT_FAILED)
          setMessageType("error")
        }
      }).finally(() => { setIsLoading(false); setShowMessageModal(false); setIsvisible(false) });     
  }
  const onClickMic = () => {
    navigation.navigate("Recording", { visitId: visit._id })
  }
  
  const onSuccessMessagecancel=useCallback(() => {
    setIsOpenMessageModal(false)
    if (onRefresh) {
      onRefresh(visit._id)
    }
  }, [onRefresh, visit._id]);
  const onMisssedVisitCancel=()=>{
    setIsvisible(false);
    setCheckValue(false);
  }
  useEffect(() => {
    // console.log(checkValue)
    setIsvisible(checkValue)
  }, [checkValue])
  return (
    <>

    <MissedVisitModal visible={isVisible} onCancel={onMisssedVisitCancel} onConfirm={onsubmit} isLoading={isLoading}/>
    <MessageModal visible={showMessageModal} onCancel={()=>setShowMessageModal(false)} message={message} type={messageType} />
    {/* <FullScreenLoader visible={isLoading} /> */}
    <DynamicMessageModalWithConfirm
                 visible={isOpenMessageModal} 
                 onCancel={onSuccessMessagecancel}
                  message={MISSED_VISIT_SUCCESS} 
                  iconComponent={<Ionicons name="checkmark-circle" size={35} color="#1D75F5" />} cancelButtonText="OK"/>
    <TouchableOpacity
      key={visit._id}
      style={styles.visitCard}
      onPress={() => onClickVisit(visit)}
    >
      <View style={[styles.visitBorder, {
        backgroundColor: getBorderColor(type).backgroundColor,
      },]} />
      <View style={styles.visitContent}>
        {/* Row with patient + EVV tag */}
        <View style={styles.topRow}>
          <View style={styles.profileBox}>
            <Text style={styles.profileInitials}>
              {visit?.clientFirstName[0] || "P"}
            </Text>
          </View>
          <View style={{ flex: 1}}>
            <Text style={styles.patientName}>
              {visit?.clientFirstName} {visit?.clientLastName} ({visit?.clientAge})
            </Text>
            <View style={styles.visitTypeContainer}>
              <Text style={styles.visitType}>{visit?.visitType}</Text>
              {/* <View style={styles.shortNameContainer}>
                <Text style={styles.shortNameText}>{visit.subName}</Text>
              </View> */}
              </View>
            </View>

            {/* {type == 'Past Due' && */}
            {/* {visit?.status == 'New' &&
            <TouchableOpacity style={styles.chatButton} onPress={onClickMic}>
              <FontAwesome name="microphone" size={20} color="#007AFF" />
            </TouchableOpacity>
            
          } */}
          </View>


        {/* <View style={styles.border} /> */}
        <View style={styles.addressContainer}>
          {/* <MapPin size={18} style={styles.timeIcon} color={"#47454D"} /> */}
          <Feather name="map-pin" size={18} color="#47454D" style={styles.timeIcon} />
          <Text style={styles.address}> {visitAddress}</Text>
        </View>

        <View style={styles.timeRangeRow}>
          <View style={styles.timeRow}>
            {/* <Clock size={18} style={styles.timeIcon} color={"#47454D"} /> */}
            <Image style={{height:18,width:18, marginLeft:2,marginRight:6}}  source={ UPCOMING_CLOCK_BLACK}/>
            <Text style={styles.timeValue}> {visit.visitStartTime ? visit.visitStartTime : ""}</Text>
            {/* <Text style={styles.timeSeperator}> - </Text>
            <Text style={styles.timeValue}>{visit?.checkOutTime}</Text> */}
            </View>
            {/* <View
            style={[
              styles.statusBadge,
              {
                backgroundColor: getBadgeColor(visit.status).backgroundColor,
              },
            ]}
          >
            <Text
              style={[
                styles.statusBadgeText,
                { color: getBadgeColor(visit.status).color },
              ]}
            >
              {visit.status}
            </Text>
          </View> */}
          </View>

          {visit?.visitStatus == 'New' &&
            <>
              {/* <View style={styles.border} /> */}
              <TouchableOpacity
                testID={`missed-visit-checkbox-${visit._id}`}
                data-testid={`missed-visit-checkbox-${visit._id}`}
                style={styles.checkboxRow}
                onPress={() => {
                  const newValue = !checkValue;
                  setCheckValue(newValue);
                }}
                activeOpacity={0.7}
              >
                <View style={[
                  styles.checkbox,
                  checkValue ? styles.checkboxChecked : styles.checkboxUnchecked,
                ]}>
                  {checkValue && (
                    <Ionicons name="checkmark" size={16} color="white" />
                  )}
                </View>
                <Text style={styles.labelText}>Missed Visit</Text>
              </TouchableOpacity>
            </>
          }
        </View>


      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  visitCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
   // overflow: "hidden",
    marginBottom: 16,
    position: "relative",
    ...globalStyles.shadow,
  },
  chatButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#EFF6FF",
  },
  visitBorder: {
    position: "absolute",
    left: 0,
    top: 16,
    bottom: 16,
    width: 5,
    // backgroundColor: "#3B82F6",
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
  },
  visitContent: {
    padding: 16,
  },
  timelineBar: {
    width: 20,
    alignItems: "center",
  },
  topRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 28,
  },
  profileBox: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#6847C1",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 18,
  },
  patientName: {
    fontFamily: "Poppins_600SemiBold",
    color: "#000",
    fontSize: 16,
    marginBottom: 2,
  },
  visitType: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 12,
    color: "#FF7882",
  },
  address: {
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
    color: "#47454D",
    marginBottom: 4,
  },
  statusBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontFamily: "Poppins_400Regular",
    fontSize: 12,
    color: "#0284C7",
  },
  timeRangeRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom:4
  },
  timeIcon: {
    marginLeft: 2,
    marginRight: 6,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom:10
  },
  timeValue: {
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
    color: "#47454D",
  },
  timeSeperator: {
    fontFamily: "Poppins_400Regular",
    marginHorizontal: 2,
  },
  border: {
    marginTop: 6,
    marginBottom: 12,
    marginHorizontal: 2,
    height: 1,
    backgroundColor: "#CBD5E1",
  },
  addressContainer: {
    display: "flex",
    // justifyContent: "space-between",
    flexDirection: "row",
    alignItems:"center",
    marginBottom:4

  },
  visitTypeContainer: {
    // display: "flex",
    // justifyContent: "space-between",
    // flexDirection: "row",
    backgroundColor: '#ff78821f',
    // padding:2,
    borderRadius: 10,
    alignSelf: 'baseline',
    paddingHorizontal: 12,
    paddingVertical: 3

  },
  shortNameContainer: {
    borderRadius: 8,
    paddingVertical: 2,
    paddingHorizontal: 6,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#f26649",
  },
  shortNameText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontFamily: "Poppins_600SemiBold",
  },
  // check box value
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    paddingVertical: 8,
    paddingRight: 10,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: THEME_COLOR,
  },
  checkboxUnchecked: {
    borderWidth: 1,
    borderColor: '#999',
  },

  labelText: {
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
    paddingLeft: 10
  },
});

export default VisitItem;
