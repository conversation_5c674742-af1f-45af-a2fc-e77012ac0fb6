{
  "title": "(O0110) - Special Treatments, Procedures, and Programs",
  "helpText": "Check all that apply on admission. Indicate whether each treatment/procedure/program is used.",
  "questions": [
    {
      "id": "cancerTreatments",
      "type": "group",
      "label": "Cancer Treatments",
      "subQuestions": [
        {
          "id": "A1_chemotherapy",
          "type": "dropdown",    // or singleSelect, checkbox, etc.
          "label": "A1. Chemotherapy",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "A2_IV",
          "type": "dropdown",
          "label": "A2. IV",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "A3_oral",
          "type": "dropdown",
          "label": "A3. Oral",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "A10_other",
          "type": "dropdown",
          "label": "A10. Other",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "B1_radiation",
          "type": "dropdown",
          "label": "B1. Radiation",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        }
      ]
    },
    {
      "id": "respiratoryTherapies",
      "type": "group",
      "label": "Respiratory Therapies",
      "subQuestions": [
        {
          "id": "C1_oxygenTherapy",
          "type": "dropdown",
          "label": "C1. Oxygen Therapy",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "C2_continuous",
          "type": "dropdown",
          "label": "C2. Continuous",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "C3_intermittent",
          "type": "dropdown",
          "label": "C3. Intermittent",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "C4_highConcentration",
          "type": "dropdown",
          "label": "C4. High-concentration",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "D1_suctioning",
          "type": "dropdown",
          "label": "D1. Suctioning",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "D2_scheduled",
          "type": "dropdown",
          "label": "D2. Scheduled",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "D3_asNeeded",
          "type": "dropdown",
          "label": "D3. As Needed",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "E1_tracheostomyCare",
          "type": "dropdown",
          "label": "E1. Tracheostomy care",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "F1_invasiveMechVentilator",
          "type": "dropdown",
          "label": "F1. Invasive Mechanical Ventilator",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "G1_nonInvasiveMechVentilator",
          "type": "dropdown",
          "label": "G1. Non-invasive Mechanical Ventilator",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "G2_biPAP",
          "type": "dropdown",
          "label": "G2. BiPAP",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        },
        {
          "id": "G3_cpap",
          "type": "dropdown",
          "label": "G3. CPAP",
          "options": [
            { "value": "", "label": "-Select-" },
            { "value": "yes", "label": "Yes" },
            { "value": "no", "label": "No" }
          ]
        }
      ]
    }
  ]
}
